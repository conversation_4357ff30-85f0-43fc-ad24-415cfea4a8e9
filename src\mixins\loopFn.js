import { rulesList, triggerTpByCode } from "@/utils/dictionary"
import { idCardReg, idCardValidator, mobileValidator } from "@/utils/validator.js"
// 循环表单通用方法
let LoopFn = {
	data() {
		return {
			// 加载中
			loadingAdForm: true,
			// 整个表单
			form: {},
			// form的验证规则
			rules: {},
			// 页面循环用list
			originList: [],
			// 非双胞胎非房产的普通字段
			normalForm: [],
			// 房产
			propertyForm: {
				// 当前选中类型
				tp: 0,
				// 上次选中类型
				lastTp: 0,
				// 当前选中类型名字
				tpCn: '',
				// 所有类型房产字段
				list: [],
				// 当前类型房产字段
				curList: [],
				// 显示类型切换弹窗
				showSelect: false,
				// 类型切换弹窗list
				selector: []
			},
			// 多胞胎，除主报名学生外最多再绑定2个人
			siblings: {
				// 所有form的表单
				allList: [],
				// 页面显示的form
				list: [],
				// 标题开关
				isHaveSib: false
			},
			sibActionsBtn: {
				width: '30%'
			},
			// 其他补充信息
			others: {
				list: []
			},
			// 随迁
			sq: {
				// 显示类型切换弹窗
				showSelect: false,
				// 类型切换弹窗list
				selector: [],
				// 当前报名类型是否是随迁
				isSQ: false,
				// 所有随迁子女的setupId
				idList: [12, 20, 28, 39, 47, 55, 68, 78, 76, 86, 84, 95, 103, 111],
				// 上次选中的类型
				lastTp: '',
				// 当前选中的类型
				tp: '',
				// 当前选中的类型
				tpIdx: 0,
				// 随迁子女类型时经商、务工、居住证信息3选1填写
				// 经商、务工、居住证
				allList: [],
				// 页面展示的类型
				list: [],
				// 页面展示的类型标题
				title: ''
			},
			adSubmitModal: {
				show: false,
				title: '报名',
				content: ''
			},
			// 提交按钮loading
			submitDisable: false
		}
	},
	methods: {
		// 字段通用处理：区分图片与非图片字段
		separateImgAndNormal(item) {
			// 非图片字段
			item._normalItem = item.leafFieldInfos.filter(fi => fi.type == 1)
			// 图片字段
			item._imgItem = item.leafFieldInfos.filter(fi => fi.type == 2)
			return item
		},
		// 字段通用处理：增加验证规则
		addValidRules(item) {
			// 循环应用验证规则
			item._normalItem.forEach(fi => {
				// 直接赋值this.form会使Vue无法绑定更新，必须用$set
				// fieldEnglish会有重复，但fieldId不会
				this.$set(this.form, `${ fi.fieldId }`, fi.fieldValue)
				// 验证条件不为0且是必输项
				if (fi.isNecessary == 1) {
					// 初始化规则
					let rule = [{ trigger: '', message: '', required: true }]
					// 输入类型
					let triggerTp = triggerTpByCode.find(v => v.id == fi.inputItemCode)
					rule[0].trigger = triggerTp.val
					rule[0].message = `请${ triggerTp.valCn }${ fi.fieldName }`
					if (fi.infoVerificationCode != 0) {
						// 身份证号
						if (fi.infoVerificationCode == 1) {
							rule.push({
								trigger: triggerTp.val,
								message: '身份证号格式不正确',
								validator: idCardValidator
							})
						} else if (fi.infoVerificationCode == 11) {
							// 手机
							rule.push({
								trigger: triggerTp.val,
								message: '手机号格式不正确',
								validator: mobileValidator
							})
						}
					}
					this.$set(this.rules, `${ fi.fieldId }`, rule)
				}
			})
			item._imgItem.forEach(fi => {
				this.$set(this.form, `${ fi.fieldId }`, fi.fieldValue)
				if (fi.isNecessary == 1) {
					// 为每个图片字
					// 段创建独立的验证规则对象，避免共享同一个对象
					let rule = {
						required: true,
						message: `请上传${ fi.fieldName }`,
						trigger: 'change'
					}
					this.$set(this.rules, `${ fi.fieldId }`, rule)
				}
			})
			return item
		},
		// 字段通用处理：删除验证规则
		removeValidRules(item) {
			// 循环删除验证规则
			item._normalItem.forEach(fi => {
				this.$delete(this.form, `${ fi.fieldId }`)
				if (fi.isNecessary == 1) {
					this.$delete(this.rules, `${ fi.fieldId }`)
				}
			})
			item._imgItem.forEach(fi => {
				this.$set(this.form, `${ fi.fieldId }`, fi.fieldValue)
				if (fi.isNecessary == 1) {
					// 为每个图片字段创建独立的验证规则对象，避免共享同一个对象
					let rule = {
						required: true,
						message: `请上传${ fi.fieldName }`,
						trigger: 'change'
					}
					this.$set(this.rules, `${ fi.fieldId }`, rule)
				}
			})
			return item
		},
		openSelector(tp) {
			this[tp].showSelect = true
		},
		closeSelector(tp) {
			this[tp].showSelect = false
		},
		// 随迁类型切换
		sqTpChange(idx) {
			// 找到当前类型
			let curTp = this.sq.selector[idx]
			this.sq.tp = curTp.id
			this.sq.tpIdx = idx
			this.sq.list = this.sq.allList.filter(v => v.typeConfigId == this.sq.tp)
			this.sq.allList.filter(v => v.typeConfigId == this.sq.lastTp).map(this.removeValidRules)
			this.sq.allList.filter(v => v.typeConfigId == this.sq.tp).map(this.addValidRules)
			this.sq.title = curTp.name
			this.sq.lastTp = this.sq.tp
			this.closeSelector('sq')
		},
		// 房产类型切换
		ppTabChange(item) {
			let cur = item.id
			this.propertyForm.tp = cur
			this.propertyForm.tpCn = this.propertyForm.selector.find(v => v.id == this.propertyForm.tp).name
			this.propertyForm.curList = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp)
			// 删除旧tab的form字段和验证规则
			this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.lastTp).map(this.removeValidRules)
			// 添加新tab的form字段和验证规则
			this.propertyForm.list.filter(v => v.typeConfigId == cur).map(this.addValidRules)
			// 更新上次点击下标
			this.propertyForm.lastTp = cur
			this.closeSelector('propertyForm')
		},
		// 选中/取消双胞胎
		initOrHideSib() {
			// 初始化第1个人
			if (this.siblings.isHaveSib) {
				// 页面更新
				this.siblings.list = this.siblings.allList.filter(v => v.typeConfigId == 3)
				// 再添加他的验证规则
				this.siblings.list.map(this.addValidRules)
			} else {
				// 删除所有
				this.siblings.list.map(this.removeValidRules)
				this.siblings.list = []
			}
		},
		// 双胞胎按钮 - 添加
		sibAdd() {
			// 第2个人
			let secondPeople = this.originList.filter(v => v.typeConfigId == 19)
			secondPeople.map(this.addValidRules)
			this.siblings.list.push(secondPeople[0])
		},
		// 双胞胎按钮 - 删除
		sibDel() {
			let secondPeople = this.originList.filter(v => v.typeConfigId == 19)
			secondPeople.map(this.removeValidRules)
			this.siblings.list.pop()
		},
		// 删除已上传图片并显示uploader
		delImg(cfg) {
			let id = cfg.fieldId
			this.form[id] = ''
			this.changeVal(id, '')
		},
		// 截取身份证号并更新页面
		// 身份证输入框值, 对应的生日域id, 对应的性别域id
		setBirthDtAndSexByIdCard(idCardVal, birthId, genderId) {
			if (idCardReg.test(idCardVal)) {
				// 年月日
				let birthDt = idCardVal.slice(6, 14)
				// 性别
				let gender = idCardVal[16]
				// 默认男
				let actualGender = '1'
				// 0和偶数为女
				if (gender == 0 || gender % 2 == 0) {
					actualGender = '2'
				}
				// 修改出生日期
				if (this.form.hasOwnProperty(birthId)) {
					// 防止页面初始化尚无$refs时报错
					if (this.$refs[birthId]) {
						this.$refs[birthId][0].dateValChange(`${birthDt.slice(0, 4)}-${birthDt.slice(4, 6)}-${birthDt.slice(6, 8)}`)
					}
				}
				// 修改性别
				if (this.form.hasOwnProperty(genderId)) {
					if (this.$refs[genderId]) {
						this.$refs[genderId][0].pickerChange({}, actualGender)
					}
				}
			}
		},
		// input值更改
		inputValueChange(v) {
			this.changeVal(v.id, v.val)
			// 基础信息 - 学生身份证号匹配
			if (v.id == '3') {
				this.setBirthDtAndSexByIdCard(v.val, '4', '5')
			}
			// 监护人信息 - 监护人1身份证号匹配
			if (v.id == '21') {
				this.setBirthDtAndSexByIdCard(v.val, '19', '17')
			}
			// 监护人信息 - 监护人2身份证号匹配
			if (v.id == '30') {
				this.setBirthDtAndSexByIdCard(v.val, '28', '26')
			}
			// 双胞胎信息 - 双胞胎1身份证号匹配
			if (v.id == '38') {
				this.setBirthDtAndSexByIdCard(v.val, '37', '35')
			}
			// 双胞胎信息 - 双胞胎2身份证号匹配
			if (v.id == '1038') {
				this.setBirthDtAndSexByIdCard(v.val, '1037', '1035')
			}
		},
		// 选择框值改变
		selectValueChange(v) {
			this.changeVal(v.id, v.val)
		},
		// 图片值改变
		uploadValueChange(v) {
			this.changeVal(v.id, v.val)
		},
		// 修改最终表单值，
		changeVal(id, val) {
			this.$set(this.form, id, val)
			this.originList.forEach(v => {
				v.leafFieldInfos.forEach(v1 => {
					if (v1.fieldId == id) {
						v1.fieldValue = val
					}
				})
			})
			let curPath = location.href
			// 是第1次报名
			if (curPath.indexOf('/ad-form/index') != -1) {
				/* console.log(this)
				console.log('loopFn的缓存数据方法里', this.originList) */
				// 整个表单
				let cacheJson = JSON.stringify(this.originList)
				// 当前报名类型
				let curAdTp = this.$store.state.entry3
				// 传给store的数据
				let cacheObj = {
					[curAdTp]: cacheJson
				}
				// console.log(cacheObj)
				this.$setCacheData4Ad(cacheObj)
			}
		},
		// 提交成功，返回首页
		back2Home() {
			uni.reLaunch({
				url: '../index/index'
			})
		},
	}
}
export { LoopFn }