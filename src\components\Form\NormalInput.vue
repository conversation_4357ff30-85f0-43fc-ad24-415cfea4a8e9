<template>
	<view class="normal-input">
		<u-input v-model="modelData" :placeholder="phTxt" @input="valChange" :maxlength="max"></u-input>
	</view>
</template>

<script>
export default {
	name: 'normal-input',
	data() {
		return {
			modelData: '',
			phTxt: '',
			// 默认不限长度
			max: -1
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		}
	},
	computed: {
		// 验证规则的快捷方式
		verCode() {
			return this.itemConfig.infoVerificationCode
		}
	},
	watch: {
		modelData(newV, oldV) {
			if (newV != oldV) {
				this.valChange(newV)
			}
		},
		'itemConfig.fieldValue': {
			handler(newV, oldV) {
				if (newV && newV != oldV) {
					this.modelData = this.itemConfig.fieldValue
				}
			},
			immediate: true
		}
	},
	created() {
		this.phTxt = `请输入${ this.itemConfig.fieldName }`
		// infoVerificationCode为1时为身份证输入
		if (this.verCode == 1) {
			this.max = 18
		}
		/* // 报名修改页面：如果有值，直接填充
		if (this.itemConfig.fieldValue) {
			this.modelData = this.itemConfig.fieldValue
		} */
	},
	methods: {
		// emit
		valChange(data) {
			this.modelData = data
			this.$emit('value-change', {
				id: this.itemConfig.fieldId,
				val: data
			})
		}
	}
}
</script>

<style scoped>
.normal-input {
	width: 100%;
}
</style>