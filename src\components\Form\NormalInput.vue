<template>
	<view class="normal-input">
		<u-input v-model="modelData" :placeholder="phTxt" @input="valChange" :maxlength="max" :type="inputType"></u-input>
	</view>
</template>

<script>
export default {
	name: 'normal-input',
	data() {
		return {
			modelData: '',
			phTxt: '',
			// 默认不限长度
			max: -1
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		}
	},
	computed: {
		// 验证规则的快捷方式
		verCode() {
			return this.itemConfig.infoVerificationCode
		},
		// 输入框类型
		inputType() {
			// 不动产权证号字段使用数字输入
			if (this.itemConfig.fieldId == 71) {
				return 'number'
			}
			return 'text'
		}
	},
	watch: {
		modelData(newV, oldV) {
			if (newV != oldV) {
				this.valChange(newV)
			}
		},
		'itemConfig.fieldValue': {
			handler(newV, oldV) {
				if (newV && newV != oldV) {
					this.modelData = this.itemConfig.fieldValue
				}
			},
			immediate: true
		}
	},
	created() {
		this.phTxt = `请输入${ this.itemConfig.fieldName }`
		// infoVerificationCode为1时为身份证输入
		if (this.verCode == 1) {
			this.max = 18
		}
		/* // 报名修改页面：如果有值，直接填充
		if (this.itemConfig.fieldValue) {
			this.modelData = this.itemConfig.fieldValue
		} */
	},
	methods: {
		// emit
		valChange(data) {
			// 不动产权证号字段只允许数字
			if (this.itemConfig.fieldId == 71) {
				// 移除非数字字符
				data = data.replace(/[^\d]/g, '')
				this.modelData = data
			} else {
				this.modelData = data
			}
			
			this.$emit('value-change', {
				id: this.itemConfig.fieldId,
				val: this.modelData
			})
		}
	}
}
</script>

<style scoped>
.normal-input {
	width: 100%;
}
</style>