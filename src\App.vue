<script>
	export default {
		onLaunch: function() {
			let tokenStartIdx = location.href.indexOf('token=')
			let token = location.href.substring(tokenStartIdx).replace('token=', '')
			// 如果有token, 就是冀时办来的
			if (tokenStartIdx > -1) {
				uni.redirectTo({
					url: `/pages/login/jsbLogin?type=jsb&token=${ token }`
				})
			}
		}
	}
</script>

<style lang="scss">
	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	@import "uview-ui/index.scss";
</style>

<style>
.uno-start {
  --un: 0;
}
/* unocss 代码生成在这 */
.uno-end {
  --un: 0;
}
</style>