<template>
  <view class="ad-detail">
    <u-skeleton rows="4" :loading="loadingAdForm">
      <!-- 区外就读特殊显示 -->
      <u-alert :title="isOutsideStudy() ? '区外就读' : `报名学校：${ adSchool }`" type="success"></u-alert>
      <view v-for="item, idx in normalForm" :key="idx" class="form-group">
        <u-alert :title="item.infoName" type="info"></u-alert>
        <view class="f-group-detail">
          <u-cell-group :border="false">
            <u-cell v-for="fi, fidx in item._normalItem" :key="fi.fieldId" :title="fi.fieldName">
              <view slot="value">
                <text v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldEnglish === 'studentIdCardNumber' ? fi.fieldValue.replace(/^swjd-/, '') : fi.fieldValue }}</text>
                <normal-select-ex :item-config.sync="fi"
                                  v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select-ex>
              </view>
            </u-cell>
          </u-cell-group>
          <u-cell-group :border="false">
            <u-cell v-for="fi, fidx in item._imgItem" :key="fi.fieldId" :title="fi.fieldName">
              <view slot="value">
                <normal-img-ex :item-config.sync="fi"></normal-img-ex>
              </view>
            </u-cell>
          </u-cell-group>
        </view>
      </view>
      <!-- 双胞胎 -->
      <view class="form-group" v-if="siblings.list.length > 0">
        <u-alert title="双胞胎" type="info"></u-alert>
        <view class="f-group-detail">
          <view class="sibs-list">
            <view class="sibs-item" v-for="si, sIdx in siblings.list" :key="sIdx">
              <view class="f-g-sub-title">双胞胎{{ sIdx + 1 }}</view>
              <u-cell-group :border="false">
                <u-cell v-for="fi, fidx in si._normalItem" :key="fi.fieldId" :title="fi.fieldName">
                  <view slot="value">
                    <text v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</text>
                    <normal-select-ex :item-config.sync="fi"
                                      v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select-ex>
                  </view>
                </u-cell>
              </u-cell-group>
              <u-cell-group :border="false">
                <u-cell v-for="fi, fidx in si._imgItem" :key="fi.fieldId" :title="fi.fieldName">
                  <view slot="value">
                    <normal-img-ex :item-config.sync="fi"></normal-img-ex>
                  </view>
                </u-cell>
              </u-cell-group>
            </view>
          </view>
        </view>
      </view>
      <!-- 房产 -->
      <view class="form-group" v-if="propertyForm.list.length > 0">
        <u-alert title="房产信息" v-if="!isOutsideStudy()" type="info"></u-alert>
        <u-alert title="证件信息" v-else type="info"></u-alert>
        <view class="f-group-detail">
          <template v-for="ppItem, ppIdx in propertyForm.list">
            <u-cell-group :border="false">
              <u-cell :title="isOutsideStudy() ? '证件类型' : '房产类型'">
                <view slot="value">{{ propertyForm.tpCn }}</view>
              </u-cell>
              <u-cell v-for="fi, fidx in ppItem._normalItem" :key="fi.fieldId" :title="fi.fieldName">
                <view slot="value">
                  <text v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</text>
                  <normal-select-ex :item-config.sync="fi"
                                    v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select-ex>
                </view>
              </u-cell>
            </u-cell-group>
            <u-cell-group :border="false">
              <u-cell v-for="fi, fidx in ppItem._imgItem" :key="fi.fieldId" :title="fi.fieldName">
                <view slot="value">
                  <normal-img-ex :item-config.sync="fi"></normal-img-ex>
                </view>
              </u-cell>
            </u-cell-group>
          </template>
        </view>
      </view>
      <template v-if="others.list.length > 0">
        <view v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId" class="form-group">
          <u-alert title="其他材料证明" type="info"></u-alert>
          <view class="f-group-detail">
            <u-cell-group :border="false">
              <u-cell v-for="fi, fidx in oItem._normalItem" :key="fi.fieldId" :title="fi.fieldName">
                <view slot="value">
                  <text v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</text>
                  <normal-select-ex :item-config.sync="fi"
                                    v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select-ex>
                </view>
              </u-cell>
            </u-cell-group>
            <u-cell-group :border="false">
              <u-cell v-for="fi, fidx in oItem._imgItem" :key="fi.fieldId" :title="fi.fieldName">
                <view slot="value">
                  <normal-img-ex :item-config.sync="fi"></normal-img-ex>
                </view>
              </u-cell>
            </u-cell-group>
          </view>
        </view>
      </template>

      <view class="form-group" v-if="youFuForm.list.length > 0">
        <u-alert title="优抚信息" type="info"></u-alert>
        <view class="f-group-detail">
          <template v-for="ppItem, ppIdx in youFuForm.list">
            <u-cell-group :border="false">
              <u-cell title="优抚类型">
                <view slot="value">{{ youFuForm.tpCn }}</view>
              </u-cell>
              <u-cell v-for="fi, fidx in ppItem._normalItem" :key="fi.fieldId" :title="fi.fieldName">
                <view slot="value">
                  <text v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</text>
                  <normal-select-ex :item-config.sync="fi" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select-ex>
                </view>
              </u-cell>
            </u-cell-group>
            <u-cell-group :border="false">
              <u-cell v-for="fi, fidx in ppItem._imgItem" :key="fi.fieldId" :title="fi.fieldName">
                <view slot="value">
                  <normal-img-ex :item-config.sync="fi"></normal-img-ex>
                </view>
              </u-cell>
            </u-cell-group>
          </template>
        </view>
      </view>

      <view class="btn-wrap">
        <u-button @click="_goBack()" type="info" text="返回"></u-button>
      </view>
      <template v-if="showModify">
        <view class="btn-wrap">
          <u-button type="primary" @click="go2EditConfirm">修改报名</u-button>
        </view>
        <view class="btn-wrap">
          <u-button type="error" @click="go2ResetConfirm">重新报名</u-button>
        </view>
      </template>
    </u-skeleton>
  </view>
</template>

<script>
import {adDetail, adRemainEditCount, isInAdTimeRange2, delAd} from '@/apis/admission'
import NormalSelectEx from "@/components/Exhibition/NormalSelectEx"
import NormalImgEx from "@/components/Exhibition/NormalImgEx"
import Timer from "@/components/timer.vue"

import NormalInput from "@/components/Form/NormalInput"
import NormalSelect from "@/components/Form/NormalSelect"
import NormalImgUpload from "@/components/Form/NormalImgUpload"
export default {
  components: {
    NormalSelectEx,
    NormalInput,
    NormalSelect,
    NormalImgUpload,
    NormalImgEx,
    Timer
  },
  data() {
    return {
      // 加载中
      loadingAdForm: true,
      // 录取查询来的报名信息
      applyRes: this.$store.state.applyQryResult,
      // 报名人
      stuId: '',
      // 报名人身份证
      stuIdCard: '',
      adSchool: '',
      // 页面循环用list
      originList: [],
      // 非双胞胎非房产的普通字段
      normalForm: [],
      youFuForm:{
        // 当前选中类型名字
        tpCn: '',
        // 所有类型房产字段
        list: [],
        // 选择器是否显示
        showSelect: false,
        // 选择器数据
        selector: [],
        // 当前选中类型
        tp: '',
        // 当前选中类型表单
        curList: []
      },
      // 房产
      propertyForm: {
        // 当前选中类型名字
        tpCn: '',
        // 所有类型房产字段
        list: []
      },
      // 多胞胎
      siblings: {
        // 页面显示的form
        list: []
      },
      // 其他补充信息
      others: {
        list: []
      },
      // 学生身份证
      stuIdCard: '',
      // 是否显示修改报名
      showModify: false,
      // 修改信息弹窗
      editConfirm: {
        show: false,
        remain: 0,
      },
      // 重新填报弹窗
      resetConfirm: {
        show: false,
        // 剩余次数
        remain: 0,
        // 倒计时时长，单位秒
        timerTotalSec: 3,
        // 默认禁用确定按钮
        confirmBtnDisable: true,
        // 确定按钮txt
        confirmBtnTxt: '',
      },
      form: {
        type: 0
      },
      entitledGroupFlag: false,
      entitledGroupType: {}
    }
  },
  created() {
    this.stuId = this.applyRes.studentId
    this.adSchool = this.applyRes.enrollSchoolName
    this.showModify = this.$store.state.showModifyTp == 2
    this.getData()
  },
  methods: {
    // 判断是否为区外就读
    isOutsideStudy() {
      return this.adSchool === '区外就读' || this.adSchool?.includes('区外就读')
    },

    // 获取次数
    async getRemainCount() {
      // 剩余可修改次数
      let count = await adRemainEditCount({key: this.stuIdCard})
      return count
    },
    // 获取表单
    getData() {
      adDetail({
        key: this.stuId
      }).then(res => {

        let {followWorkType, enrollMiddleFieldVos, entitledGroupForm} = res;
        if (entitledGroupForm) {
          let {value, fields} = entitledGroupForm;
          fields = JSON.parse(fields);
          this.entitledGroupFlag = true;
          this.form.type = value;
          this.entitledGroupType = fields;
        }
        res = enrollMiddleFieldVos;
        // 按typeConfigId从小到大排序
        let resCopy = JSON.parse(JSON.stringify(res)).sort((a, b) => a.typeConfigId - b.typeConfigId)
        // 分类
        this.originList = resCopy.map(this.separateImgAndNormal)
        // 取基础信息里的学生身份证
        this.stuIdCard = resCopy.find(v => v.typeConfigId == 1).leafFieldInfos.find(v => v.fieldId == 3).fieldValue
        
        // 获取上学信息字段
        const schoolInfoFields = this.originList.filter(v => v.typeConfigId == 25)
        
        // 非房产非双胞胎的普通字段，typeConfigId为1，2，4，5，6，7。直接添加验证规则
        let basicFields = this.originList.filter(v => v.typeConfigId < 8 && v.typeConfigId != 3 && v.typeConfigId != 25)
        
        // 将上学信息放在最前面
        this.normalForm = [...schoolInfoFields, ...basicFields]
        
        // 房产字段：typeConfigId >= 8但小于19
        this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 && v.typeConfigId < 18)
        // 双胞胎字段：typeConfigId为3和19
        this.siblings.list = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
        // 优抚字段：typeConfigId >= 20但小于25
        this.youFuForm.list = this.originList.filter(v => v.typeConfigId >= 20 &&  v.typeConfigId < 25)
        if (this.youFuForm.list.length > 0) {
          // 初始化优抚选择器
          this.youFuForm.selector = this.youFuForm.list.map(v => {
            return {
              name: v.infoName,
              id: v.typeConfigId
            }
          })
          // 默认选中第一个类型
          this.youFuForm.tp = this.youFuForm.list[0].typeConfigId
          // 当前选中类型中文
          this.youFuForm.tpCn = this.youFuForm.list[0].infoName
          // 当前选中类型表单
          this.youFuForm.curList = [this.youFuForm.list[0]]
        }
        // 如果有房产
        if (this.propertyForm.list.length > 0) {
          // 当前选中类型中文
          this.propertyForm.tpCn = this.propertyForm.list[0].infoName
        }

        // 经商或者务工只二选一，【6：务工信息，5: 经商信息】
        if (followWorkType && followWorkType === 5) {
          this.normalForm = this.normalForm.filter(item => item.typeConfigId !== "6")
        } else if (followWorkType && followWorkType === 6) {
          this.normalForm = this.normalForm.filter(item => item.typeConfigId !== "5")
        }

        // 其他补充信息
        this.others.list = this.originList.filter(v => v.typeConfigId == 18)
        this.loadingAdForm = false
      })
    },
    // 字段通用处理：区分图片与非图片字段
    separateImgAndNormal(item) {
      // 非图片字段
      item._normalItem = item.leafFieldInfos.filter(fi => fi.type == 1)
      // 图片字段
      item._imgItem = item.leafFieldInfos.filter(fi => fi.type == 2 && fi.fieldValue)
      return item
    },
    // 前往修改确认页
    go2EditConfirm() {
      let params = {
        idCard: this.stuIdCard
      }
      uni.navigateTo({
        url: './editConfirm',
        success(res) {
          res.eventChannel.emit('acceptDataFromOpenerPage', params)
        }
      })
    },
    // 打开选择器
    openSelector(type) {
      this[type].showSelect = true
    },
    // 关闭选择器
    closeSelector(type) {
      this[type].showSelect = false
    },
    // 优抚类型切换
    yfTabChange(e) {
      const selectedType = this.youFuForm.selector[e.index].id
      this.youFuForm.tp = selectedType
      this.youFuForm.tpCn = this.youFuForm.selector[e.index].name
      this.youFuForm.curList = this.youFuForm.list.filter(v => v.typeConfigId == selectedType)
      this.closeSelector('youFuForm')
    },
    // 输入框值改变
    inputValueChange(v) {
      // 查找并更新字段值
      this.updateFieldValue(v.id, v.val)
    },
    // 选择框值改变
    selectValueChange(v) {
      // 查找并更新字段值
      this.updateFieldValue(v.id, v.val)
    },
    // 上传图片值改变
    uploadValueChange(v) {
      // 查找并更新字段值
      this.updateFieldValue(v.id, v.val)
    },
    // 更新字段值
    updateFieldValue(fieldId, value) {
      // 遍历所有表单组
      const updateField = (items) => {
        if (!items) return false

        for (let i = 0; i < items.length; i++) {
          // 检查普通项
          if (items[i]._normalItem) {
            for (let j = 0; j < items[i]._normalItem.length; j++) {
              if (items[i]._normalItem[j].fieldId === fieldId) {
                items[i]._normalItem[j].fieldValue = value
                return true
              }
            }
          }

          // 检查图片项
          if (items[i]._imgItem) {
            for (let j = 0; j < items[i]._imgItem.length; j++) {
              if (items[i]._imgItem[j].fieldId === fieldId) {
                items[i]._imgItem[j].fieldValue = value
                return true
              }
            }
          }
        }
        return false
      }

      // 依次检查各个表单组
      updateField(this.normalForm) ||
      updateField(this.siblings.list) ||
      updateField(this.propertyForm.list) ||
      updateField(this.youFuForm.list) ||
      updateField(this.others.list)
    },
    // 前往重报确认页
    go2ResetConfirm() {
      let params = {
        idCard: this.stuIdCard
      }
      uni.navigateTo({
        url: './resetConfirm',
        success(res) {
          res.eventChannel.emit('acceptDataFromOpenerPage', params)
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.ad-detail {
  padding: 10px;

  .form-group {
    margin-top: 10px;
  }

  .f-g-sub-title {
    margin-top: 10px;
    text-align: center;
    font-size: 13px;
    color: #CCC;
  }

  .btn-wrap {
    margin-bottom: 10px;
  }
}
</style>