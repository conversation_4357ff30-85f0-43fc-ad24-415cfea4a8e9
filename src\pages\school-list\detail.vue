<template>
	<view class="school-detail">
		<view class="s-nm">{{ schoolNm }}</view>
		<view class="">学校地址：{{ schoolAddr }}</view>
		<view class="">学校联系电话：{{ schoolTel }}</view>
		<u-parse :content="schoolContent"></u-parse>
	</view>
</template>

<script>
import { schoolDetail } from "@/apis/admission.js"
export default {
	data() {
		return {
			id: this.$store.state.schoolDetail.id,
			schoolNm: '',
			schoolAddr: '',
			schoolTel: '',
			schoolContent: ''
		}
	},
	created() {
		this.getData()
	},
	methods: {
		// 获取详情
		getData() {
			schoolDetail({
				key: this.id
			}).then(res => {
				this.schoolNm = res.deptName
				this.schoolAddr = res.address
				this.schoolTel = res.mobile
				this.schoolContent = res.content
			})
		}
	}
}
</script>

<style scoped lang="scss">
.school-detail {
	padding: 10px;
	font-size: 14px;
	.s-nm {
		text-align: center;
		font-size: 16px;
		margin-bottom: 10px;
	}
}
</style>