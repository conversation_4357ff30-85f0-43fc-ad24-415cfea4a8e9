/**
 * 报名类型分类工具（数据已填充）
 */
export const registrationCategories = {
    // 数据集合（根据你的数据填充）
    KINDERGARTEN_IDS: [6, 33, 62, 89],          // 幼儿园ID集合
    SCHOOL_TYPE_IDS: [7, 34, 63, 90],           // 小学类型ID集合
    JUNIOR_SCHOOL_TYPE_IDS: [8, 35, 64, 91],    // 初中类型ID集合
    ENTITLED_GROUP_IDS: [13, 21, 29, 40, 48, 56, 69, 77, 85, 96, 104, 112],  // 优抚对象ID集合
    FOLLOW_WORK_TYPE_IDS: [12, 20, 28, 39, 47, 55, 68, 76, 84, 95, 103, 111],// 随迁子女ID集合
    TOWN_REGISTRATION_IDS: [2],                 // 乡镇报名ID集合
    CITY_REGISTRATION_IDS: [3],                 // 城区报名ID集合
    PUBLIC_SCHOOL_IDS: [4, 60],                 // 公办ID集合
    PRIVATE_SCHOOL_IDS: [5, 61],                // 民办ID集合
    HOUSEHOLD_CONDITION_IDS: [9, 17, 25, 36, 44, 52, 65, 73, 81, 92, 100, 108], // 学生户籍条件
    HOUSEHOLD_IDS: [10, 18, 26, 37, 45, 53, 66, 74, 82, 93, 101, 109],          // 户口ID集合
    PROPERTY_CONDITION_IDS: [11, 19, 27, 38, 46, 54, 67, 75, 83, 94, 102, 110], // 房产条件
    RENTAL_HOUSING_IDS: [14, 22, 30, 41, 49, 57, 70, 78, 86, 97, 105, 113],     // 拆迁租房
    LOCAL_EMPLOYMENT_IDS: [],                   // 小学本县务工（空数组）
    OTHER_IDS: [16, 24, 32, 43, 51, 59, 72, 80, 88, 99, 107, 115],              // 其他类型

    // ---------- 判断方法 ----------

    /** 判断是否为优抚对象 */
    isEntitledGroup: (id) => {
        return registrationCategories.ENTITLED_GROUP_IDS.includes(Number(id));
    },

    /** 判断是否为乡镇报名 */
    isTownRegistration: (id) => registrationCategories.TOWN_REGISTRATION_IDS.includes(Number(id)),

    /** 判断是否为城区报名 */
    isCityRegistration: (id) => registrationCategories.CITY_REGISTRATION_IDS.includes(Number(id)),

    /** 判断是否为公办学校 */
    isPublicSchool: (id) => registrationCategories.PUBLIC_SCHOOL_IDS.includes(Number(id)),

    /** 判断是否为民办学校 */
    isPrivateSchool: (id) => registrationCategories.PRIVATE_SCHOOL_IDS.includes(Number(id)),

    /** 判断是否为幼儿园 */
    isKindergarten: (id) => registrationCategories.KINDERGARTEN_IDS.includes(Number(id)),

    /** 判断是否为小学类型 */
    isSchoolType: (id) => registrationCategories.SCHOOL_TYPE_IDS.includes(Number(id)),

    /** 判断是否为初中类型 */
    isJuniorSchoolType: (id) => registrationCategories.JUNIOR_SCHOOL_TYPE_IDS.includes(Number(id)),

    /** 判断是否为随迁子女 */
    isFollowWorkType: (id) => registrationCategories.FOLLOW_WORK_TYPE_IDS.includes(Number(id)),

    /** 判断是否以学生户籍为基本条件 */
    isHouseholdCondition: (id) => registrationCategories.HOUSEHOLD_CONDITION_IDS.includes(Number(id)),

    /** 判断是否为户口类型 */
    isHousehold: (id) => registrationCategories.HOUSEHOLD_IDS.includes(Number(id)),

    /** 判断是否以房产为基本条件 */
    isPropertyCondition: (id) => registrationCategories.PROPERTY_CONDITION_IDS.includes(Number(id)),

    /** 判断是否为拆迁在外租房 */
    isRentalHousing: (id) => registrationCategories.RENTAL_HOUSING_IDS.includes(Number(id)),

    /** 判断是否为小学本县务工 */
    isLocalEmployment: (id) => registrationCategories.LOCAL_EMPLOYMENT_IDS.includes(Number(id)),

    /** 判断是否为其他类型 */
    isOther: (id) => registrationCategories.OTHER_IDS.includes(Number(id))
};
