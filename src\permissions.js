import store from '@/store'
import LoginApis from '@/apis/modules/user.js'
import { baseUrl } from '@/utils/common.js'

/**
 * 注意：拦截uni.switchTab本身没有问题。
 * 但是在微信小程序端点击tabbar的底层逻辑并不是触发uni.switchTab。
 * 所以误认为拦截无效，此类场景的解决方案是在tabbar页面的页面生命周期onShow中处理。
 */
export default {
  install(Vue) {
    const whileList = ["/", "/pages/area-select/index", "/pages/index/index", "/pages/login/auth", "/pages/login/jsbLogin"];
    let list = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];
    list.forEach(item => {
      uni.addInterceptor(item, {
        async invoke(e) {
          if (whileList.includes(e.url)) {
            return e;
          } else {
            if (store.state.token) {
							return e
            } else {
              store.commit('SET_TARGETURL', e.url) // 存储目标url
              LoginApis.getWxConfig().then(res => {
								console.log(res)
								store.commit('SET_HOST', res.redirectHost)
                window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res.appId}&redirect_uri=${res.redirectHost}/${ baseUrl }/pages/login/auth&response_type=code&scope=snsapi_userinfo&state=${res.state}#wechat_redirect`
              })
            }
          }
					// return e
        },
        fail(error) {
          console.log('addInterceptor fail', error)
        }
      })
    })

  }
}