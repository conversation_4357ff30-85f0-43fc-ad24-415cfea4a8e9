<template>
	<view class="entrance">
		<!-- 页面标题提示框 -->
		<u-alert title="区外就读报名申请" type="success" effect="dark"></u-alert>


		<!-- 加载状态 -->
		<view v-if="loadingAdForm" class="loading-container">
			<text>正在加载表单数据...</text>
		</view>

		<!-- 表单内容 -->
		<view v-else>
			<u-form :model="form" ref="form" :rules="rules" labelPosition="top" labelWidth="auto">
				<!-- 基础信息表单 -->
				<view class="form-group" v-for="item, idx in normalForm" :key="idx">
					<view class="fake-title">
						<text>{{ item.infoName }}</text>
					</view>
					<view class="f-group-detail">
						<template v-for="fi, fidx in item._normalItem">
							<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
								<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
								<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
							</u-form-item>
						</template>
						<template v-for="fi, fidx in item._imgItem">
							<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
								<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
							</u-form-item>
						</template>
					</view>
				</view>

				<!-- 房产信息 -->
				<view class="form-group" v-if="propertyForm.list.length > 0">
					<view class="fake-title">
						<text>证件信息</text>
					</view>
					<view class="f-group-detail">
						<!-- 房产类型选择器 -->
						<view class="normal-select fixed-type" @click="openSelector('propertyForm')">
							<u-action-sheet :actions="propertyForm.selector" title="房产类型" :show="propertyForm.showSelect"
											@select="ppTabChange" @close="closeSelector('propertyForm')"
											:closeOnClickOverlay="false"></u-action-sheet>
							<view class="ph-txt">证件类型</view>
							<view class="fake-input">
								<view class="res-txt">{{ propertyForm.tpCn }}</view>
								<text class="fake-input-icon">
									<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
								</text>
							</view>
						</view>
						<!-- 房产字段 -->
						<view v-for="ppItem, ppIdx in propertyForm.curList" :key="ppItem.typeConfigId">
							<template v-for="fi, fidx in ppItem._normalItem">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
									<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
								</u-form-item>
							</template>
							<template v-for="fi, fidx in ppItem._imgItem">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
								</u-form-item>
							</template>
						</view>
					</view>
				</view>

				<!-- 双胞胎 -->
				<view class="form-group" v-if="siblings.allList.length > 0">
					<view class="fake-title">
						<text>双胞胎</text>
						<u-switch v-model="siblings.isHaveSib" @change="initOrHideSib" :disabled="siblings.allList.length === 0"></u-switch>
					</view>
					<view class="f-group-detail">
						<view class="sibs-list">
							<view class="sibs-item" v-for="si, sIdx in siblings.list" :key="sIdx">
								<view class="f-g-sub-title">双胞胎{{ sIdx + 1 }}</view>
								<template v-for="fi, fidx in si._normalItem">
									<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
										<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
										<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
									</u-form-item>
								</template>
								<template v-for="fi, fidx in si._imgItem">
									<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
										<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
									</u-form-item>
								</template>
							</view>
						</view>
						<view class="sib-actions">
							<u-button type="primary" class="sib-actions-btn add" text="添加" @click="sibAdd" v-if="siblings.list.length == 1"></u-button>
							<u-button type="error" class="sib-actions-btn del" text="删除" @click="sibDel" v-if="siblings.list.length == 2"></u-button>
						</view>
					</view>
				</view>

				<!-- 其他补充信息 -->
				<view class="form-group" v-for="item, idx in otherForm" :key="idx">
					<view class="fake-title">
						<text>{{ item.infoName }}</text>
					</view>
					<view class="f-group-detail">
						<template v-for="fi, fidx in item._normalItem">
							<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
								<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
								<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
							</u-form-item>
						</template>
						<template v-for="fi, fidx in item._imgItem">
							<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
								<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
							</u-form-item>
						</template>
					</view>
				</view>
			</u-form>

      <u-button @click="submitAdForm" text="提交" :disabled="submitDisable"></u-button>
		
		<!-- 提交成功弹窗 -->
		<u-modal :show="adSubmitModal.show" :title="adSubmitModal.title" :content="adSubmitModal.content" @confirm="back2Home"></u-modal>
	</view>
</view>
</template>

<script>
import { adFormDetail, submitAd, submitAdSecond, getNextPath } from '@/apis/admission'
import NormalInput from "@/components/Form/NormalInput"
import NormalSelect from "@/components/Form/NormalSelect"
import NormalImgUpload from "@/components/Form/NormalImgUpload"
import { LoopFn } from '@/mixins/loopFn.js'

export default {
	components: {
		NormalInput,
		NormalSelect,
		NormalImgUpload
	},
	mixins: [LoopFn],
	data() {
		return {
			// setupId 固定为 52
			entryId: 116,
			loadingAdForm: false,
			submitDisable: false,
			// 基础表单数据
			normalForm: [],
			// 双胞胎数据
			siblings: {
				isHaveSib: false,
				allList: [],
				list: []
			},
			// 其他补充信息
			otherForm: [],
			// 原始数据
			originList: [],
			// 提交成功弹窗
			adSubmitModal: {
				show: false,
				title: '提示',
				content: '申请提交成功'
			}
		}
	},
	created() {
		this.getData()
	},
	methods: {
		// 获取表单数据
		getData() {
			this.loadingAdForm = true
      adFormDetail({
				key: this.entryId
			}).then(res => {
				console.log('区外就读表单数据:', res)
				// 按typeConfigId从小到大排序并分类
				this.originList = res.sort((a, b) => a.typeConfigId - b.typeConfigId).map(this.separateImgAndNormal)
        
        // 获取上学信息字段
        const schoolInfoFields = this.originList.filter(v => v.typeConfigId == 25)
        
				// 基础信息字段 - 不包含上学信息字段(25)
				let normalIdList = [1, 2, 4, 5, 6, 7].map(v => `${v}`)
				this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
        
        // 如果有上学信息字段，添加到normalForm的最前面
        if(schoolInfoFields.length > 0) {
          this.normalForm = [...schoolInfoFields.map(this.addValidRules), ...this.normalForm]
        }

				// 房产字段：typeConfigId >= 8但小于18
				this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 && v.typeConfigId < 18)

				// 双胞胎字段：typeConfigId为3和19
				this.siblings.allList = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)

				// 其他补充信息（除了基础信息、双胞胎、房产信息之外的）
				this.otherForm = this.originList.filter(v => {
					return normalIdList.indexOf(v.typeConfigId) == -1 &&
						   v.typeConfigId != 3 && v.typeConfigId != 19 &&
						   !(v.typeConfigId >= 8 && v.typeConfigId < 18) &&
               v.typeConfigId != 25 // 排除上学信息字段
				}).map(this.addValidRules)

				// 如果有房产信息
				if (this.propertyForm.list.length > 0) {
					// 初始化房产选择器
					this.propertyForm.selector = this.propertyForm.list.map(v => {
						return {
							name: v.infoName,
							id: v.typeConfigId
						}
					})
          
					// 房产默认选中第1个tab
					this.propertyForm.tp = this.propertyForm.list[0].typeConfigId
					this.propertyForm.lastTp = this.propertyForm.tp
					// 当前选中类型中文
					this.propertyForm.tpCn = this.propertyForm.selector.find(v => v.id == this.propertyForm.tp).name
					// 当前选中类型表单
					this.propertyForm.curList = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp)
					// 添加第1个tab的验证规则
					this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp).map(this.addValidRules)
				}

				this.loadingAdForm = false
			}).catch(err => {
				this.loadingAdForm = false
				console.error('获取区外就读表单数据失败:', err)
			})
		},

		// 提交申请
    submitAdForm() {
			this.submitDisable = true
			let params = {
				setUpSaveIds: this.entryId,
				enrollSchoolId: 999999,
				enrollSchoolName: "区外就读",
				userId: this.$store.state.userInfo.id,
				enrollMiddleFieldFormList: this.originList,
				houseType: this.propertyForm.tp // 房产类型
			}

			// 没选双胞胎就去掉上传字段
			if (!this.siblings.isHaveSib) {
				params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 3 && v.typeConfigId != 19)
			}

			console.log('区外就读提交参数:', params)

      this.$refs['form'].validate().then(async valid => {
        // 查询当前学生是否报过名
        let isStuAd = await getNextPath({key: this.$store.state.userId})
        if (isStuAd == 0) {
          submitAd(params).then(res => {
            this.adSubmitModal.content = '报名成功'
            this.submitDisable = false
            this.adSubmitModal.show = true
            this.$setCacheData4Ad({})
          }).catch(err => {
            this.submitDisable = false
          })
        } else {
          submitAdSecond(params).then(res => {
            this.adSubmitModal.content = '修改报名成功'
            this.submitDisable = false
            this.adSubmitModal.show = true
          }).catch(err => {
            this.submitDisable = false
          })
        }
      }).catch(validErr => {
        this.submitDisable = false
      })
		},

		// 返回首页
		back2Home() {
			this.adSubmitModal.show = false
			uni.reLaunch({
				url: '/pages/index/index'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.label-text{
  color: #909399;
  font-size: 12px;
  margin-bottom: 5px;
}
.entrance {
  padding: 10px;

  .form-group {
    margin-top: 10px;
  }

  .fake-title {
    position: relative;
    padding: 8px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    color: #909399;
    border-radius: 4px;
    background-color: #f4F4F5;
  }

  .sq-selector {
    width: 50%;
  }

  .sibs-item {
    padding: 10px;
    border-bottom: 1px dashed #dcdfe6;
  }

  .sib-actions {
    margin-top: 10px;

    .sib-actions-btn {
      width: 30%;
    }
  }

  .f-g-sub-title {
    font-style: italic;
    color: #CCC;
  }

  .fixed-type {
    margin-bottom: 10px;

    .ph-txt {
      margin-top: 15px;
      margin-bottom: 5px;
      font-size: 15px;
    }
  }
}

.outside-study-alert {
	margin: 10px 0;
}

.submit-area {
	padding: 20px;
	margin-top: 20px;
}

.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 200px;
	color: #666;
}
</style>
