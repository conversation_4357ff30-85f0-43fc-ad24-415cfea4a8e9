<template>
	<view class="hotline">
		<u-subsection :list="list" :current="cur" @change="tabChange"></u-subsection>
		<u-list @scrolltolower="nextPage" class="s-list" v-if="mainList.list.length > 0">
			<u-list-item v-for="(item, idx) in mainList.list" :key="idx" class="s-item">
				<view class="s-name">{{ item.deptName }}</view>
				<view>{{ item.mobile }}</view>
			</u-list-item>
		</u-list>
		<u-empty mode="list" text="暂无学校" v-else></u-empty>
	</view>
</template>

<script>
import { schoolList } from "@/apis/admission.js"
import List from '@/mixins/list.js'
export default {
	mixins: [List],
	data() {
		return {
			list: ['小学咨询电话', '初中咨询电话'],
			cur: 0,
			// 小学
			search2: {
			  period: '2',
			  pageNumber: 1,
			  pageSize: 10,
			  level: 3,
			  deptCode: this.$store.state.deptCode,
			  type: "1"
			},
			// 初中
			search3: {
			  period: '3',
			  pageNumber: 1,
			  pageSize: 10,
			  level: 3,
			  deptCode: this.$store.state.deptCode,
			  type: "1"
			},
			// 幼儿园
			search1: {
			  period: '1',
			  pageNumber: 1,
			  pageSize: 10,
			  level: 3,
			  deptCode: this.$store.state.deptCode,
			  type: "1"
			}
		}
	},
	computed: {
		// 激活下标与学段参数对应字典
		// 2：小学，3：初中，1：幼儿园
		curPeriod() {
			return ['2', '3', '1'][this.cur]
		}
	},
	created() {
		// 涉县才有幼儿园
		if (this.$store.state.deptCode == '130426') {
			this.list.push('幼儿园咨询电话')
		}
		this.getList()
	},
	methods: {
		// 重置查询
		resetQry() {
			this.mainList.list = []
			this.mainList.total = 0
			this.mainList.alLoad = 0
			this.search1.pageNumber = 1
			this.search2.pageNumber = 1
			this.search3.pageNumber = 1
			this.getList()
		},
		// 翻页
		nextPage() {
			if (this.mainList.alLoad != this.mainList.total) {
				// 翻页
				this[`search${ this.curPeriod }`].pageNumber++
				this.mainList.status = 'loading'
				this.getList()
			}
		},
		// tab
		tabChange(idx) {
			this.cur = idx
			this.resetQry()
		},
		// 列表
		getList() {
			schoolList(this[`search${ this.curPeriod }`]).then(res => {
				let newPageData = res.records
				this.mainList.list = this.mainList.list.concat(newPageData)
				// 不足一页
				if (newPageData.length < this[`search${ this.curPeriod }`].pageSize) {
					this.mainList.alLoad = this.mainList.total
					this.mainList.status = 'nomore'
				} else {
					this.mainList.alLoad = this.mainList.list.length
				}
				uni.stopPullDownRefresh()
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.hotline {
	padding: 10px;
	.s-list {
		margin-top: 15px;
	}
	.s-item {
		padding: 10px;
		border-bottom: 1px solid #dcdfe6;
		background-color: #FFF;
	}
	.s-name {
		color: #909399;
		font-size: 14px;
		margin-bottom: 5px;
	}
}
</style>