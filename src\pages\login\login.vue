<template>
    <div></div>
</template>

<script>
import store from '@/store'
import LoginApis from '@/apis/modules/user.js'
import { baseUrl } from '@/utils/common.js'
export default {
    name: 'Login',
    data() {
        return {}
    },
    onReady() {
      uni.clearStorage();
      LoginApis.getWxConfig().then(res => {
        console.log(res)
        store.commit('SET_HOST', res.redirectHost)
        window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res.appId}&redirect_uri=${res.redirectHost}/${ baseUrl }/pages/login/auth&response_type=code&scope=snsapi_userinfo&state=${res.state}#wechat_redirect`
      })
    },
    methods: {}
}
</script>

<style lang='scss' scoped></style>