<template>
	<view class="entry2">
		<view class="entrance-list">
			<u-cell-group>
				<u-cell v-for="item, idx in entryList" :key="item.id" :isLink="true" :title="item.name"  @click="go2NewPage(item.id)">
				</u-cell>
			</u-cell-group>
		</view>
		<u-toast ref="entryTips"></u-toast>
	</view>
</template>

<script>
import { qryEntry, isInAdTimeRange1, getNextPath } from '@/apis/admission'
export default {
	data() {
		return {
			entryId: this.$store.state.entry1,
			entryList: []
		}
	},
	created() {
		this.$store.commit('SET_ENTRY2', '')
		this.$store.commit('SET_ENTRY3', '')
		this.$store.commit('SET_SCHOOL_DETAIL', {})
		this.getEntrance()
	},
	methods: {
		// 获取报名入口
		getEntrance() {
			qryEntry({
				key: this.entryId
			}).then(res => {
				this.entryList = res
			})
		},
		// 跳转
		async go2NewPage(entryId) {
			let isStuAd = await getNextPath({ key: this.$store.state.userId })
			if (isStuAd == 0) {
				isInAdTimeRange1().then(res => {
					if (entryId == 7) {
						if (res.villagesPrimarySchool) {
							this.go2Next(entryId)
						} else {
							this.notInTime()
						}
					} else if (entryId == 8) {
						if (res.villagesJuniorSchool) {
							this.go2Next(entryId)
						} else {
							this.notInTime()
						}
					} else if (entryId == 63) {
						if (res.urbanPrimarySchool) {
							this.go2Next(entryId)
						} else {
							this.notInTime()
						}
					} else if (entryId == 64) {
						if (res.urbanJuniorSchool) {
							this.go2Next(entryId)
						} else {
							this.notInTime()
						}
					}
				})
			} else if (isStuAd == 4) {
				this.go2Next(entryId)
			} else {
				this.$refs.entryTips.show({
					message: '您已报名，修改信息请从录取查询处进入修改',
					type: 'warning',
					duration: 3000
				})
			}
		},
		// 不在时间内
		notInTime() {
			this.$refs.entryTips.show({
				message: '当前入口不在报名时间内',
				type: 'error',
				duration: 3000
			})
		},
		// 下一页
		go2Next(id) {
			this.$store.commit('SET_ENTRY2', id)
			uni.navigateTo({
				url: `/pages/entry3/index`
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.entry2 {
	box-sizing: border-box;
	.entrance-list {
		padding-top: 20px;
		width: 100%;
	}
}
</style>