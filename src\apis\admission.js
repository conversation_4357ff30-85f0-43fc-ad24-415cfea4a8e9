import request from "@/utils/request"
import store from "@/store"
import { pref } from '@/utils/common'

// 查询表单
export const adFormDetail = (data) => request.post(`${ pref }${store.state.deptCode}/biz/enrollment/getEnrollFieldConfig`, data)

// 提交报名
export const submitAd = (data) => request.post(`${ pref }${store.state.deptCode}/biz/enrollment/submitEnrollInfo`, data)

// 报名详情
export const adDetail = data => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/getStuEnrollInfo`, data)

// 查询报名入口：首页(1级)，乡镇城区(2级)
export const qryEntry = (data) => request.post(`${ pref }${store.state.deptCode}/biz/enrollment/getSetupSaveIds`, data)
export const xiaoquList = params => request.post('/user-api/center/schoolRang/xiaoquList', params)

// 查询报名入口：报名类别(3级)
export const qryEntryDetail = (data) => request.post(`${ pref }${store.state.deptCode}/biz/enrollment/getSetupSaveDetail`, data)

// 学校列表
export const schoolList = (data) => request.post('/user-api/center/dept/schoolPageList', data)
// // 学校列表
export const deptPageLists = (data) => request.post('/user-api/center/dept/pageList', data)

// 学校详情
export const schoolDetail = data => request.post('/user-api/center/dept/detail', data)

// 依据学校的指定招生范围
export const schoolAdRange = data => request.post(`/user-api/center/schoolRang/list`, data)

// 录取查询
export const qryFirst = (data) => request.post(`${pref}${store.state.deptCode}/biz/enrollQuery/onePage`, data)

// 录取查询 - 根据身份证号查询
export const searchResById = (data) => request.post(`${pref}${store.state.deptCode}/biz/enrollQuery/search`, data)

// 录取查询 - 录取通知书
export const notifyDetail = (data) => request.post(`${pref}${store.state.deptCode}/biz/enrollQuery/requisition`, data)

// 录取查询 - 绑定指定身份证号到当前微信
export const bindIdCard2CurWx = params => request.post(`${ pref }${store.state.deptCode}/biz/enrollQuery/stuBindingWeChat`, params)

// 删除上传的图片
export const delImgUpload = (data) => request.post(`${pref}${ store.state.deptCode }/biz/file/deleteImg`, data)

// 是否报名时间内
export const isInAdTimeRange1 = (data) => request.post(`${pref}${store.state.deptCode}/biz/registrationTime/homePageVerify`, data)

// 是否报名时间内
export const isInAdTimeRange2 = (data) => request.post(`${pref}${store.state.deptCode}/biz/registrationTime/getVerifyBySetUpId`, data)

// 驳回或修改后的报名提交
export const submitAdSecond = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/rejectOrModifyEnrollInfo`, params)

// 录取查询 - 获取跳转类型
export const getNextPath = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/getNextPath`, params)

// 报名详情页 - 剩余修改次数
export const adRemainEditCount = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/getResidueModifyCount`, params)

// 报名详情页 - 删除报名数据
export const delAd = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/parentClearEnrollInfo`, params)

// 根据学生id查询已报名表单
export const qryEditAdForm = params => request.post(`${pref}${store.state.deptCode}/biz/enrollment/modifyGetEnrollFieldConfig`, params)

// 报名修改页 - 获取所有报名类型
export const getAllAdTp = params => request.post(`${pref}${ store.state.deptCode }/biz/enrollment/getEnrollEntrance`, params)