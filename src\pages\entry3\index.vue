<template>
	<view class="entry3">
		<view class="entrance-list">
			<u-cell-group>
				<u-cell v-for="item, idx in entryList" :key="item.setupId" :isLink="true" @click="go2NewPage(item.setupId)">
					<view slot="title" class="entry-title">{{ item.idsName }}</view>
					<view slot="label" class="entry-detail">
						<view class="time">报名开始时间：{{ item.startTime }}</view>
						<view class="time">报名结束时间：{{ item.endTime }}</view>
						<view class="detail-txt">{{ item.detail }}</view>
					</view>
				</u-cell>
			</u-cell-group>
		</view>
		<u-toast ref="entryTips"></u-toast>
	</view>
</template>

<script>
import { qryEntryDetail, isInAdTimeRange2, getNextPath } from '@/apis/admission'
export default {
	data() {
		return {
			entryId: this.$store.state.entry2,
			entryList: []
		}
	},
	created() {
		this.$store.commit('SET_ENTRY3', '')
		this.$store.commit('SET_SCHOOL_DETAIL', {})
		this.getEntrance()
	},
	methods: {
		// 获取报名入口
		getEntrance() {
			qryEntryDetail({
				key: this.entryId
			}).then(res => {
				this.entryList = this.sortEntranceList(res);
			})
		},
    // 排序报名类别列表
    sortEntranceList(list) {
      // 定义排序优先级
      const orderMap = {
        '第一批次:两统一适龄儿童报名入口': 1,
        '第二批次:户口房产不一致适龄儿童报名入口': 2,
        '第三批次:祖父母/外祖父母房产适龄儿童报名入口': 3,
        '第四批次:滦南有房产无户籍适龄儿童报名入口': 4,
        '第五批次:随迁及其他情况入学适龄儿童报名入口': 5,
        '随迁子女(滦南有房产)适龄儿童报名入口': 6,
        '其他适龄儿童报名入口': 7
      };

      // 对列表进行排序
      return [...list].sort((a, b) => {
        const orderA = orderMap[a.idsName] || 999; // 未定义的类型放最后
        const orderB = orderMap[b.idsName] || 999;
        return orderA - orderB;
      });
    },
		// 跳转
		async go2NewPage(entryId) {
			let isStuAd = await getNextPath({ key: this.$store.state.userId })
			if (isStuAd == 0) {
				isInAdTimeRange2({
					key: entryId
				}).then(res => {
					if (!res) {
						this.$refs.entryTips.show({
							message: '当前入口不在报名时间内',
							type: 'error',
							duration: 3000
						})
					} else {
						// 检查缓存
						let cache = this.$getCacheData4Ad(entryId)
						// 缓存为空
						if (JSON.stringify(cache) == '{}') {
							let newCache = {
								[entryId]: {}
							}
							this.$setCacheData4Ad(newCache)
						}
						this.$store.commit('SET_ENTRY3', entryId)
						uni.navigateTo({
							url: `/pages/school-list/list`
						})
					}
				})
			} else if (isStuAd == 4) {
				this.$store.commit('SET_ENTRY3', entryId)
				uni.navigateTo({
					url: `/pages/school-list/list`
				})
			} else {
				this.$refs.entryTips.show({
					message: '您已报名，修改信息请从录取查询处进入修改',
					type: 'warning',
					duration: 3000
				})
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.entry3 {
	box-sizing: border-box;
	.entrance-list {
		padding-top: 20rpx;
		width: 100%;
		.entry-title {
			color: #398ade;
		}
		.entry-detail {
			margin-top: 5px;
			color: #909193;
			font-size: 13px;
			.detail-txt {
				color: #606266;
				margin-top: 5px;
			}
		}
	}
}
</style>