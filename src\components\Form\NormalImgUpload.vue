<template>
	<view class="normal-img-upload">
		<view class="upload-succ-list">
			<view class="upload-succ-item" v-for="item, idx in list" :key="idx">
				<image :src="item.fullUrl" @click="imgPreview(idx)"></image>
				<view class="del-btn" @click="removePic(idx)">×</view>
			</view>
			<view class="upload-succ-item" @click="chooseImg" v-if="list.length < maxCount">
				<u-icon name="camera-fill" color="#d3d4d6" size="26"></u-icon>
			</view>
		</view>
		<view class="sample-img" v-if="sampleUrl">
			<u-button plain size="mini" type="primary" text="示例图" @click="previewSample"></u-button>
		</view>
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
import store from "@/store"
import { pref, imgPrefix } from "@/utils/common"
import { delImgUpload } from "@/apis/admission.js"
import * as imageConversion from 'image-conversion'
export default {
	name: "normal-img-upload",
	data() {
		return {
			action: `${ process.env.VUE_APP_BASE_API }${ pref }${store.state.deptCode}/biz/file/uploadImg`,
			host: this.$store.state.appHost,
			list: [],
			accept: 'image',
			maxCount: 1,
			maxSize: 6,
			sampleUrl: ''
		}
	},
	props: {
	  // 字段所有配置
	  itemConfig: {
	    type: Object,
	    required: true,
	  },
	  // 多选
	  multiple: {
	    type: Boolean,
	    default: false,
	  },
	  // 最大上传个数
	  limit: {
	    type: Number,
	    default: 1,
	  },
	},
	created() {
		this.maxCount = this.limit
		if (this.itemConfig.fieldValue) {
			this.list = [{
				fullUrl: `${ this.host }${ imgPrefix() }${ this.itemConfig.fieldValue }`,
				url: this.itemConfig.fieldValue
			}]
		}
		if (this.itemConfig.sampleImgUrl) {
			this.sampleUrl = `${ this.host }${ imgPrefix() }${ this.itemConfig.sampleImgUrl }`
		}
	},
	methods: {
		// 删除
		removePic(idx) {
			let that = this
			uni.showModal({
				title: '提示',
				content: '确定要删除吗?',
				success: (r) => {
					if (r.confirm) {
						delImgUpload({
						  key: this.list[idx].url
						}).then((res) => {
						  this.valChange("")
							this.list.splice(idx, 1)
						})
					}
				}
			})
		},
		// 选择图片
		chooseImg() {
			let that = this
			uni.chooseImage({
				count: 1,
				sourceType: ['album', 'camera'],
				success: function (res) {
					let selected = res.tempFiles[0]
					let selectedUrl = res.tempFilePaths[0]
					// 小于6M
					if (selected.size < that.maxSize * 1024 * 1024) {
						// 大于200k
						if (selected.size > 200 * 1024) {
							// 压缩至200kb
							imageConversion.compressAccurately(selected, 200).then(res => {
								let newFile = new File([ res ], { type: res.type })
								let url = window.URL.createObjectURL(newFile)
								that.publicUpload(url)
							})
						} else {
							that.publicUpload(selectedUrl)
						}
					} else {
						that.$refs.uToast.show({
							message: `图片超出${ that.maxSize }MB上限`
						})
					}
				}
			})
		},
		// 上传
		publicUpload(url) {
			return new Promise((resolve, reject) => {
				uni.showLoading({
					title: '上传中'
				})
				const uploadTask = uni.uploadFile({
					url: this.action,
					filePath: url,
					fileType: 'image',
					name: 'file',
					header: {
						'Authorization': `${ store.state.token }`,
						'client-id': 'wx',
						'state': store.state.state4Headers
					},
					timeout: 3600000,
					success: (res) => {
						uni.hideLoading()
						if (res.statusCode == 200) {
							let resUrl = JSON.parse(res.data)
							this.valChange(resUrl.data)
							this.list.push({
								// 手动拼接绝对路径
								// 使用相对路径时，路径中出现'/h5/'(根目录)会导致图片访问失败
								fullUrl: `${ this.host }${ imgPrefix() }${ resUrl.data }`,
								url: resUrl.data
							})
							resolve(res)
						} else {
							this.valChange('')
							reject(res)
						}
					},
					fail: (err) => {
						uni.hideLoading()
						this.valChange('')
						reject(err)
					},
					complete: () => {
						uni.hideLoading()
					}
				})
				uploadTask.onProgressUpdate((res) => {
					uni.showLoading({
						title: '上传中' + res.progress + '%'
					})
					if (res.progress == 100) {
						uni.hideLoading()
					}
				})
			})
			
		},
		// 预览已上传
		imgPreview(idx) {
			let arr = this.list.map(v => v.fullUrl)
			uni.previewImage({
				urls: arr,
				current: idx
			})
		},
		// 示例图
		previewSample() {
			let arr = [this.sampleUrl]
			uni.previewImage({
				urls: arr,
				current: 0
			})
		},
		// emit
		valChange(data) {
		  this.$emit("value-change", {
		    id: this.itemConfig.fieldId,
		    val: data
		  })
		},
	}
}
</script>

<style>
</style>