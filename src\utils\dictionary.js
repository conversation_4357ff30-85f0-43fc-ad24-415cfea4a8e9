// 与学生关系
let relationList = [
	{ id: '1',  val: '父子' },
	{ id: '2',  val: '父女' },
	{ id: '3',  val: '母子' },
	{ id: '4',  val: '母女' },
	{ id: '5',  val: '本人' },
	{ id: '6',  val: '其他' },
	{ id: '7',  val: '祖父母' },
	{ id: '8',  val: '外祖父母' },
	{ id: '9',  val: '弟弟' },
	{ id: '10', val: '妹妹' },
	{ id: '11', val: '哥哥' },
	{ id: '12', val: '姐姐' }
]

// 与学生关系
const StudentRelationship = [
	{
		id: '父子',
		val: '父子',
	},
	{
		id: '母子',
		val: '母子'
	},
	{
		id: '父女',
		val: '父女'
	},
	{
		id: '母女',
		val: '母女'
	},
	{
		id: '祖孙',
		val: '祖孙',
	},
	{
		id: '外祖孙',
		val: '外祖孙',
	},
]

// 性别
let genderList = [
	{ id: '1', val: '男' },
	{ id: '2', val: '女' },
]

// 是否
let booleanList = [
	{ id: '0', val: '否' },
	{ id: '1', val: '是' },
]

// 年级
let gradeList = [
	{ id: '1', val: '一年级' },
	{ id: '2', val: '二年级' },
	{ id: '3', val: '三年级' },
	{ id: '4', val: '四年级' },
	{ id: '5', val: '五年级' },
	{ id: '6', val: '六年级' },
	{ id: '7', val: '初一' },
	{ id: '8', val: '初二' },
	{ id: '9', val: '初三' }
]

// 所有区县
let areaList = [
	{ id: '130202', val: '路南区' },
	{ id: '130203', val: '路北区' },
	{ id: '130204', val: '古冶区' },
	{ id: '130403', val: '开平区' },
	{ id: '130208', val: '丰润区' },
	{ id: '130207', val: '丰南区' },
	{ id: '130281', val: '遵化市' },
	{ id: '130229', val: '玉田县' },
	{ id: '130283', val: '迁安市' },
	{ id: '130227', val: '迁西县' },
	{ id: '130284', val: '滦州市' },
	{ id: '130224', val: '滦南县' },
	{ id: '130225', val: '乐亭县' },
	{ id: '130209', val: '曹妃甸区' },
	{ id: '130271', val: '芦台开发区' },
	{ id: '130272', val: '汉沽管理区' },
	{ id: '130273', val: '高新开发区' },
	{ id: '130274', val: '海港开发区' },
	{ id: '130285', val: '南堡开发区' }
]

// 验证规则们
let rulesList = [
	// 0：无格式限制
	{ },
	// 1：身份证号
	{
		required: true,
		trigger: 'blur'
	},
	// 2：出生日期
	{
		required: true,
		message: '请选择出生日期',
		trigger: 'change'
	},
	// 3：性别
	{
		required: true,
		message: '请选择性别',
		trigger: 'change',
		list: genderList
	},
	// 4：是否
	{
		required: true,
		trigger: 'change',
		list: booleanList
	},
	// 5：一年级至初三
	{
		required: true,
		message: '请选择年级',
		trigger: 'change',
		list: gradeList
	},
	// 6：选择小学，学校列表
	{
		required: true,
		message: '请选择小学',
		trigger: 'change',
		list: []
	},
	// 7：选择幼儿园，学校列表
	{
		required: true,
		message: '请选择幼儿园',
		trigger: 'change',
		list: []
	},
	// 8：选择学校招生范围，小区列表
	{
		required: true,
		message: '请选择学校招生范围',
		trigger: 'change',
		list: []
	},
	// 9：选择关系：父子，父女，母子，母女，本人
	{
		required: true,
		message: '请选择关系',
		trigger: 'change',
		list: relationList.slice(0, 5)
	},
	// 10：选择关系：父子，父女，母女，母子，其他
	{
		required: true,
		message: '请选择关系',
		trigger: 'change',
		list: relationList.slice(0, 4).concat(relationList[5])
	},
	// 11：手机号
	{
		required: true,
		trigger: 'blur'
	},
	// 12：选择关系：弟弟，妹妹，哥哥，姐姐
	{
		required: true,
		message: '请选择关系',
		trigger: 'change',
		list: relationList.slice(8)
	},
	// 13：选择关系：父子，父女，母子，母女，祖父母，外祖父母，本人
	{
		required: true,
		message: '请选择关系',
		trigger: 'change',
		list: relationList.slice(0, 4).concat(relationList[6], relationList[7], relationList[4], relationList[5])
	},
	// 14：日期时间
	{
		required: true,
		message: '请选择时间',
		trigger: 'change'
	},
	// 15：图片验证
	{
		required: true,
		message: '请上传图片',
		trigger: 'change'
	},
	// 16：关系，优抚类型专用
	{
		required: true,
		message: '请选择关系',
		trigger: 'change',
		list: relationList.slice(0,4)
	},
]

// 表单域的inputItemCode字段的值对应的验证规则
// id: inputItemCode字段的值，val: 验证规则里的trigger类型，valCn：验证规则里的message中文
let triggerTpByCode = [
	{
		id: '1',
		val: 'blur',
		valCn: '输入'
	},
	{
		id: '2',
		val: 'change',
		valCn: '选择'
	},
	{
		id: '3',
		val: 'change',
		valCn: '选择'
	},
	{
		id: '4',
		val: 'blur',
		valCn: '输入'
	},
	{
		id: '5',
		val: 'change',
		valCn: '选择'
	},
	{
		id: '6',
		val: 'change',
		valCn: '选择'
	}
]


export {
	relationList,
	rulesList,
	genderList,
	booleanList,
	gradeList,
	areaList,
	triggerTpByCode
}