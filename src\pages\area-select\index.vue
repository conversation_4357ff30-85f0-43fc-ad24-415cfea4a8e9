<template>
	<view class="area-select">
		<view>当前区县：{{ curVal }}</view>
		<u-list>
			<!-- <u-list-item>
				<u-cell-group>
					<u-cell title="民办中小学报名入口" :isLink="true" arrow-direction="right" @click="go2Index('mb')"></u-cell>
				</u-cell-group>
			</u-list-item> -->
			<u-list-item v-for="item, idx in list" :key="item.id">
				<u-cell-group>
					<u-cell :title="item.val" :value="item.id" :isLink="item.id != cur" arrow-direction="right" @click="go2Index(item)"></u-cell>
				</u-cell-group>
			</u-list-item>
		</u-list>
	</view>
</template>

<script>
import { areaList } from '@/utils/dictionary.js'
export default {
	data() {
		return {
			cur: this.$store.state.deptCode,
			curVal: '',
			list: areaList
		}
	},
	created() {
		this.curVal = areaList.find(v => v.id == this.cur).val
	},
	methods: {
		// 入口
		go2Index(item) {
			this.$store.commit('SET_DEPTCODE', item.id)
			uni.reLaunch({
				url: '/pages/index/index'
			})
		}
	}
}
</script>

<style>
</style>