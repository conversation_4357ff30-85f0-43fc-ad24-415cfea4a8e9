<template>
	<view class="entrance">
		<u-alert :title="`当前报名学校：${ adSchool }`" type="success" effect="dark"></u-alert>
		<u-alert title="报名类型" type="info" style="margin-top: 10px;"></u-alert>
		<view class="normal-select fixed-type en-tp">
			<u-action-sheet :actions="allEnrollTp" title="报名类型" :show="enrollAllTp.showSelect" @select="enrollTpChange" @close="closeSelector('enrollAllTp')" :closeOnClickOverlay="false"></u-action-sheet>
			<view class="ph-txt">报名类型</view>
			<view class="tp-select">
				<view class="fake-input" @click="openSelector('enrollAllTp')">
					<view class="res-txt">{{ enrollAllTp.tpCn }}</view>
					<text class="fake-input-icon">
						<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
					</text>
				</view>
				<view class="tp-actions">
					<u-button type="primary" :disabled="curEnrollTp == curPageEnrollTp" @click="openTpSwitchConfirm">确认修改</u-button>
				</view>
			</view>
		</view>
		<u-skeleton rows="10" :loading="loadingAdForm">
			<u-form :model="form" :rules="rules" ref="form" labelPosition="top" errorType="toast" labelWidth="auto">
				<!-- 非房产非双胞胎的普通字段 -->
				<view v-for="item, idx in normalForm" :key="idx" class="form-group">
					<u-alert :title="item.infoName" type="info"></u-alert>
					<view class="f-group-detail">
						<template v-for="fi, fidx in item._normalItem">
							<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
								<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
								<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
							</u-form-item>
              <div v-if="fi.fieldId == 125" class="field-tipss">
                注：请填写毕业小学全称
              </div>
						</template>
						<template v-for="fi, fidx in item._imgItem">
							<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
								<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
							</u-form-item>
						</template>
					</view>
				</view>
				<!-- 双胞胎 -->
				<view class="form-group" v-if="siblings.allList.length > 0">
					<view class="fake-title">
						<text>双胞胎</text>
						<u-switch v-model="siblings.isHaveSib" @change="initOrHideSib"></u-switch>
					</view>
					<view class="f-group-detail">
						<view class="sibs-list">
							<view class="sibs-item" v-for="si, sIdx in siblings.list" :key="sIdx">
								<view class="f-g-sub-title">双胞胎{{ sIdx + 1 }}</view>
								<template v-for="fi, fidx in si._normalItem">
									<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
										<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
										<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
									</u-form-item>
								</template>
								<template v-for="fi, fidx in si._imgItem">
									<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
										<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
									</u-form-item>
								</template>
							</view>
						</view>
						<view class="sib-actions">
							<u-button type="primary" class="sib-actions-btn add" text="添加" @click="sibAdd" v-if="siblings.list.length == 1"></u-button>
							<u-button type="error" class="sib-actions-btn del" text="删除" @click="sibDel" v-if="siblings.list.length == 2"></u-button>
						</view>
					</view>
				</view>
				<!-- 随迁 -->
				<view class="form-group" v-if="sq.isSQ && sq.allList.length > 0">
					<view class="fake-title">
						<text>{{ sq.title }}</text>
						<view class="sq-selector">
							<u-subsection @change="sqTpChange" :list="sq.selector" keyName="name" :current="sq.tpIdx"></u-subsection>
						</view>
					</view>
					<view class="f-group-detail">
						<view v-for="sqItem, sqIdx in sq.list" :key="sqItem.typeConfigId">
							<view v-for="fi, fidx in sqItem._normalItem" :key="fi.fieldId">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
									<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
								</u-form-item>
							</view>
							<view v-for="fi, fidx in sqItem._imgItem" :key="fi.fieldId">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
								</u-form-item>
							</view>
						</view>
					</view>
				</view>
				<!-- 房产 -->
				<view class="form-group" v-if="propertyForm.list.length > 0">
					<u-alert title="房产信息" type="info"></u-alert>
					<view class="normal-select fixed-type" @click="openSelector('propertyForm')">
						<u-action-sheet :actions="propertyForm.selector" title="房产类型" :show="propertyForm.showSelect" @select="ppTabChange" @close="closeSelector('propertyForm')" :closeOnClickOverlay="false"></u-action-sheet>
						<view class="ph-txt">房产类型</view>
						<view class="fake-input">
							<view class="res-txt">{{ propertyForm.tpCn }}</view>
							<text class="fake-input-icon">
								<u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
							</text>
						</view>
					</view>
					<view class="f-group-detail">
						<view v-for="ppItem, ppIdx in propertyForm.curList" :key="ppItem.typeConfigId">
							<view v-for="fi, fidx in ppItem._normalItem" :key="fi.fieldId">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
									<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
								</u-form-item>
								<!-- 为不动产权证书的房屋坐落地址添加提示，单独放在下方 -->
								<view v-if="propertyForm.tp == 9 && fi.fieldId == 148" class="field-tip">
                  注：地址从小区名称开始填写
								</view>
												<!-- 为不动产权证号添加提示 -->
												<view v-if="propertyForm.tp == 9 && fi.fieldId == 71" class="field-tip">
                  注：请输入不动产权证第X号中的数字部分
								</view>
                <!-- 为房屋所有权添加提示 -->
                <view v-if="propertyForm.tp == 8 && fi.fieldId == 65" class="field-tip">
                  注：请输入房产证编号中'X字第'后面的数字部分
                </view>
                <view v-if="propertyForm.tp == 8 && fi.fieldId == 145" class="field-tip">
                  注：地址从小区名称开始填写
                </view>
							</view>
							<view v-for="fi, fidx in ppItem._imgItem" :key="fi.fieldId">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
								</u-form-item>
							</view>
						</view>
					</view>
				</view>
				<view class="form-group" v-if="others.list.length > 0">
					<u-alert title="其他材料证明" type="info"></u-alert>
					<view class="f-group-detail">
						<view v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId">
							<template v-for="fi, fidx in oItem._normalItem">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
									<normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
								</u-form-item>
							</template>
							<template v-for="fi, fidx in oItem._imgItem">
								<u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
									<normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
								</u-form-item>
							</template>
						</view>
					</view>
				</view>
			</u-form>
			<u-button @click="submitAdForm" :disabled="submitDisable">提交</u-button>
		</u-skeleton>
		<u-modal :show="adSubmitModal.show" :title="adSubmitModal.title" :content='adSubmitModal.content' @confirm="back2Home"></u-modal>
		<u-modal :showCancelButton="true" @cancel="closeTpSwitchConfirm" :show="enrollTpSwitchConfirm.show" :title="enrollTpSwitchConfirm.title" :content='enrollTpSwitchConfirm.content' @confirm="confirmEditEnTp" ></u-modal>
	</view>
</template>

<script>
import { qryEditAdForm, submitAdSecond, getAllAdTp } from '@/apis/admission'
import NormalInput from "@/components/Form/NormalInput"
import NormalSelect from "@/components/Form/NormalSelect"
import NormalImgUpload from "@/components/Form/NormalImgUpload"
import { LoopFn } from "@/mixins/loopFn"
export default {
	components: { 
		NormalInput,
		NormalSelect,
		NormalImgUpload
	},
	mixins: [ LoopFn ],
	data() {
		return {
			// 所有报名类型
			allEnrollTp: [],
			// 当前报名类型, 选择框的值
			curEnrollTp: '',
			// 页面展示的报名类型,
			// 切换了新报名类型，填写完信息再去修改curEnrollTp会导致提交类型与页面展示类型不一致
			curPageEnrollTp: '',
			// 录取查询来的报名信息
			applyRes: this.$store.state.applyQryResult,
			adSchool: '',
			enrollAllTp: {
				showSelect: false,
				tpCn: '--'
			},
			enrollTpSwitchConfirm: {
				show: false,
				title: '提示',
				content: '部分已填信息将被清空，确定修改报名类型？'
			}
		}
	},
	created() {
		this.adSchool = this.applyRes.enrollSchoolName
		this.curEnrollTp = this.applyRes.registrationTypeId
		this.curPageEnrollTp = this.applyRes.registrationTypeId
		// 是否随迁类型
		this.sq.isSQ = this.sq.idList.some(v => v == this.curEnrollTp)
		this.getData()
		this.getAllEnrollTp()
	},
	methods: {
		getAllEnrollTp() {
			getAllAdTp({
				key: this.curEnrollTp
			}).then(res => {
				let list = res.map(v => {
					return {
						name: v.idsName,
						id: v.setupId
					}
				})
				this.allEnrollTp = list
				this.enrollAllTp.tpCn = list.find(v => v.id == this.curEnrollTp).name
			})
		},
		// 报名类型修改
		enrollTpChange(item) {
			this.curEnrollTp = item.id
			this.enrollAllTp.tpCn = item.name
		},
		// 打开报名类型确定
		openTpSwitchConfirm() {
			this.enrollTpSwitchConfirm.show = true
		},
		// 关闭报名类型确定
		closeTpSwitchConfirm() {
			this.enrollTpSwitchConfirm.show = false
		},
		// 确定修改报名类型
		confirmEditEnTp() {
			// 重置房产
			this.propertyForm.tp = 0
			this.propertyForm.lastTp = 0
			this.propertyForm.tpCn = 0
			this.propertyForm.list = []
			this.propertyForm.curList = []
			this.propertyForm.selector = []
			// 重置双胞胎
			this.siblings.allList = []
			this.siblings.list = []
			this.siblings.isHaveSib = false
			// 重置其它
			this.others.list = []
			// 重置随迁
			this.sq.selector = []
			this.sq.isSQ = this.sq.idList.some(v => v == this.curEnrollTp)
			this.sq.lastTp = ''
			this.sq.tp = ''
			this.sq.tpIdx = ''
			this.sq.list = []
			this.sq.allList = []
			this.loadingAdForm = true
			this.closeTpSwitchConfirm()
			this.getData()
		},
		// 获取表单
		getData() {
			qryEditAdForm({
				setupId: this.curEnrollTp,
				studentId: this.applyRes.studentId
			}).then(res => {
				// 按typeConfigId从小到大排序
				let resCopy = JSON.parse(JSON.stringify(res.enrollMiddleFieldVoList)).sort((a, b) => a.typeConfigId - b.typeConfigId)
				this.curPageEnrollTp = this.curEnrollTp
				// 分类
				this.originList = resCopy.map(this.separateImgAndNormal)
				// 是随迁
				if (this.sq.isSQ) {
					// 排除经商5、务工6、居住证7，改为三选一
					let normalIdList = [1, 2, 4].map(v => `${ v }`)
					this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
					this.sq.allList = this.originList.filter(v => v.typeConfigId == 5 || v.typeConfigId == 6 || v.typeConfigId == 7)
					// 如果配置了经商、务工、居住证
					if (this.sq.allList.length > 0) {
						// 重新排序：居住证信息放在最前面
						let sortedList = []
						// 先添加居住证信息
						let residencePermit = this.sq.allList.find(v => v.typeConfigId == 7)
						if (residencePermit) {
							sortedList.push(residencePermit)
						}
						// 再添加经商和务工信息
						let business = this.sq.allList.find(v => v.typeConfigId == 5)
						if (business) {
							sortedList.push(business)
						}
						let work = this.sq.allList.find(v => v.typeConfigId == 6)
						if (work) {
							sortedList.push(work)
						}

						this.sq.selector = sortedList.map(v => {
							return {
								name: v.infoName,
								id: v.typeConfigId
							}
						})
						let selected = ''
						// 其它不包含经商务工的报名类型修改为随迁，followWorkType为0
						if (res.followWorkType == 0) {
							// 那就默认选中第1个
							selected = this.sq.allList[0].typeConfigId
						} else {
							// 否则直接取
							selected = res.followWorkType
						}
						let selectedIdx = this.sq.selector.findIndex(v => v.id == selected)
						this.sqTpChange(selectedIdx)
					}
				} else {
					let normalIdList = [1, 2, 4, 5, 6, 7].map(v => `${ v }`)
					this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
				}
				// 房产字段：typeConfigId >= 8但小于19
				this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 &&  v.typeConfigId < 18)
				// 双胞胎字段：typeConfigId为3和19
				this.siblings.allList = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
				if (this.siblings.allList.length > 0) {
					// 深拷贝模板数据
					this.siblings.list = JSON.parse(JSON.stringify(this.siblings.allList));

					// 检查是否是真实数据（非空白模板）
					const hasRealData = this.siblings.list.some(sibling => {
						// 检查普通字段
						const hasNormalValue = sibling._normalItem?.some(field =>
							field.fieldValue !== undefined &&
							field.fieldValue !== null &&
							field.fieldValue !== ''
						);
						// 检查图片字段
						const hasImgValue = sibling._imgItem?.some(field =>
							field.fieldValue !== undefined &&
							field.fieldValue !== null &&
							field.fieldValue !== ''
						);
						return hasNormalValue || hasImgValue;
					});
					this.cleanEmptySiblings();
					// 设置初始勾选状态
					this.siblings.isHaveSib = hasRealData;

					// 如果是空白模板，清空list（但仍保留allList供用户手动勾选时使用）
					if (!hasRealData) {
						this.siblings.list = [];
					}
				}
				// 如果有房产
				if (this.propertyForm.list.length > 0) {
					// 初始化房产选择器
					this.propertyForm.selector = this.propertyForm.list.map(v => {
						return {
							name: v.infoName,
							id: v.typeConfigId
						}
					})
					// 如果上个报名类型没有房产字段
					if (res.houseInfoType == 0) {
						// 选中第1个房产类型
						this.propertyForm.tp = this.propertyForm.list[0].typeConfigId
					} else {
						this.propertyForm.tp = `${ res.houseInfoType }`
					}
					this.propertyForm.lastTp = this.propertyForm.tp
					// 当前选中类型中文
					this.propertyForm.tpCn = this.propertyForm.selector.find(v => v.id == this.propertyForm.tp).name
					// 当前选中类型表单
					this.propertyForm.curList = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp)
					// 添加第1个tab的验证规则
					this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp).map(this.addValidRules)
				}
				// 其他补充信息
				this.others.list = this.originList.filter(v => v.typeConfigId == 18)
				if (this.others.list.length > 0) {
					this.others.list.map(this.addValidRules)
				}
				this.loadingAdForm = false
			})
		},
		// 提交
		submitAdForm() {
			 this.submitDisable = true
			let params = {
				setUpSaveIds: this.curPageEnrollTp,
				enrollSchoolId: this.applyRes.enrollSchoolId,
				enrollSchoolName: this.applyRes.enrollSchoolName,
				userId: this.$store.state.userInfo.id,
				enrollMiddleFieldFormList: this.originList,
				houseType: this.propertyForm.tp,
				followWorkType: ''
			}
			// 没选双胞胎就去掉上传字段
			if (!this.siblings.isHaveSib) {
				params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 3 && v.typeConfigId != 19)
			} else if (this.siblings.list.length == 1) {
				// 只有一条时删掉id是19的
				params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 19)
			}
			// 是随迁传入选中的随迁类型
			if (this.sq.isSQ) {
				params.followWorkType = this.sq.tp

				// 过滤经商务工居住证信息：只保留选中的类型
				const selectedWorkType = this.sq.tp; // 当前选中的类型ID (5=经商, 6=务工, 7=居住证)
				console.log('编辑提交 - 选中的工作类型:', selectedWorkType);

				// 移除未选中的经商、务工、居住证信息
				params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(item => {
					// 保留非经商务工居住证的信息
					if (item.typeConfigId != 5 && item.typeConfigId != 6 && item.typeConfigId != 7) {
						return true;
					}
					// 只保留选中的类型信息
					return item.typeConfigId == selectedWorkType;
				});

				console.log('编辑提交 - 过滤后的表单数据:', params.enrollMiddleFieldFormList.map(item => ({
					typeConfigId: item.typeConfigId,
					infoName: item.infoName
				})));
			}
			this.$refs['form'].validate().then(valid => {
				submitAdSecond(params).then(res => {
					this.adSubmitModal.content = '修改报名成功'
					this.submitDisable = false
					this.adSubmitModal.show = true
				}).catch(err => {
					this.submitDisable = false
				})
			}).catch(err => {
				this.submitDisable = false
			})
		},
		// 清理空的双胞胎数据
		cleanEmptySiblings() {
			this.siblings.list = this.siblings.list.filter(sibling => {
				const hasNormalItem = sibling._normalItem && sibling._normalItem.some(item =>
					item.fieldValue !== null && item.fieldValue !== undefined && item.fieldValue !== ""
				);
				const hasImgItem = sibling._imgItem && sibling._imgItem.some(item =>
					item.fieldValue !== null && item.fieldValue !== undefined && item.fieldValue !== ""
				);
				return hasNormalItem || hasImgItem; // 保留至少一个有数据的项
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.entrance {
	padding: 10px;
	.fake-title {
		position: relative;
		padding: 8px 10px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 14px;
		font-weight: bold;
		color: #909399;
		border-radius: 4px;
		background-color: #f4F4F5;
	}
	.sq-selector {
		width: 75%;
	}
	.sibs-item {
		padding: 10px;
		border-bottom: 1px dashed #dcdfe6;
	}
	.sib-actions {
		margin: 10px 0;
		.sib-actions-btn {
			width: 30%;
		}
	}
	.f-g-sub-title {
		font-style: italic;
		color: #CCC;
	}
	.fixed-type {
		margin-bottom: 10px;
		.ph-txt {
			margin-top: 15px;
			margin-bottom: 5px;
			font-size: 15px;
		}
	}
	.en-tp {
		.tp-select {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.fake-input {
			flex: 0 0 68%;
		}
		.tp-actions {
			flex: 0 0 30%;
		}
	}
  .field-tipss {
    color: red;
    font-size: 12px;
    margin-top: -5px;
    margin-bottom: 10px;
    padding-left: 10px;
  }
	.field-tip {
		color: red;
		font-size: 12px;
		margin-top: -5px;
		margin-bottom: 10px;
		padding-left: 10px;
	}
}
</style>