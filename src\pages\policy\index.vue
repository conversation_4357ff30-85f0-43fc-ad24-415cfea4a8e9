<template>
	<view class="policy">
		<u-list @scrolltolower="nextPage">
			<u-list-item v-for="(item, idx) in mainList.list" :key="idx">
				<u-cell-group>
					<u-cell :title="item.title" :isLink="true" arrow-direction="right" @click="go2Detail(item)"></u-cell>
				</u-cell-group>
			</u-list-item>
		</u-list>
		<!-- <view class="load-more" @click="nextPage">{{ mainList.status }}</view>
		<u-loadmore v-if="mainList.list.length != 0" :margin-top="20" @loadmore="nextPage()" :icon="false" font-size="14" color="#949494" :status="mainList.status" :load-text="mainList.loadTxt" :marginBottom="20" /> -->
		<u-empty class="empty" text="暂无消息" mode="message" v-if="mainList.list.length == 0"></u-empty>
	</view>
</template>

<script>
import { policyList } from '@/apis/other'
import List from '@/mixins/list.js'
export default {
	mixins: [List],
	data() {
		return {
			search: {
				type: 1,
				title: ''
			}
		}
	},
	created() {
		this.getList()
	},
	methods: {
		// 列表
		getList() {
			policyList(this.search).then(res => {
				let newPageData = res.records
				this.mainList.list = this.mainList.list.concat(newPageData)
				// 不足一页
				if (newPageData.length < this.search.pageSize) {
					this.mainList.alLoad = this.mainList.total
					this.mainList.status = 'nomore'
				} else {
					this.mainList.alLoad = this.mainList.list.length
				}
				uni.stopPullDownRefresh()
			})
		},
		// 详情
		go2Detail(row) {
			uni.navigateTo({
				url: './detail',
				success (res) {
					res.eventChannel.emit('acceptDataFromOpenerPage', row)
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
.policy {
	
}
.load-more {
	padding: 10px 0;
	background-color: #FFF;
	text-align: center;
}
</style>