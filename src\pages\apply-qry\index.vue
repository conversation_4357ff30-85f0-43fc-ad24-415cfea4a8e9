<template>
	<view class="apply-qry">
		<template v-if="pageTp == 0">
			<u-skeleton
				rows="3"
				title
				loading
				:animate="true"
			></u-skeleton>
		</template>
		<view v-else-if="pageTp == 1">
			<u-search @change="searchById" placeholder="请输入身份证号" v-model="search.idCardNumber" maxlength="18"></u-search>
		</view>
		<view v-else-if="pageTp == 2">
			<u-cell-group>
				<u-cell title="录取状态">
					<template slot="right-icon">
						<u-tag v-if="searchResult.enrollStatus == 1" type="info" :text="searchResult.enrollStatusName"></u-tag>
						<u-tag v-else-if="searchResult.enrollStatus == 2" type="success" :text="searchResult.enrollStatusName"></u-tag>
						<u-tag v-else-if="searchResult.enrollStatus == 3" type="error" :text="searchResult.enrollStatusName"></u-tag>
					</template>
				</u-cell>
				<u-cell title="审核状态">
					<template slot="right-icon">
						<u-text type="primary" :text="searchResult.auditStatusName" v-if="searchResult.auditStatus == 1"></u-text>
						<u-text type="success" :text="searchResult.auditStatusName" v-else-if="searchResult.auditStatus == 2"></u-text>
						<u-text type="error" :text="searchResult.auditStatusName" v-else-if="searchResult.auditStatus == 3"></u-text>
					</template>
				</u-cell>
				<template v-if="searchResult.auditStatus == 3">
					<template v-if="searchResult.schoolRejectCause && searchResult.educationRejectCause">
						<u-cell title="学校驳回原因" >
							<template slot="label">
								<u-text type="error" :text="searchResult.schoolRejectCause"></u-text>
							</template>
						</u-cell>
						<u-cell title="教育局驳回原因" >
							<template slot="label">
								<u-text type="error" :text="searchResult.educationRejectCause"></u-text>
							</template>
						</u-cell>
					</template>
					<template v-else>
						<u-cell title="驳回原因" >
							<template slot="label">
								<u-text type="error" :text="searchResult.rejectCause"></u-text>
							</template>
						</u-cell>
					</template>
				</template>
				<u-cell title="学生姓名" :value="searchResult.studentName"></u-cell>
				<u-cell title="报名类型" :value="searchResult.registrationType"></u-cell>
				<u-cell title="身份证号" :value="searchResult.studentIdCardNumber"></u-cell>
				<u-cell title="报名学校" :value="searchResult.enrollSchoolName"></u-cell>
				<u-cell title="录取学校" :value="searchResult.lastSchoolName"  v-if="searchResult.enrollStatus == 2"></u-cell>
				<!-- 录取成功时显示二维码 -->
				<u-cell title="录取二维码" v-if="searchResult.enrollStatus == 2 && searchResult.qrcodeImage">
					<template slot="label">
						<view class="qrcode-container">
							<image
								:src="searchResult.qrcodeImage"
								mode="aspectFit"
								class="qrcode-image"
								@click="previewQRCode"
							></image>
							<view class="qrcode-tip">点击查看大图</view>
						</view>
					</template>
				</u-cell>
				<u-cell title="报名时间" :value="searchResult.enrollTime"></u-cell>
				<u-cell title="审核时间" :value="searchResult.auditTime" v-if="searchResult.auditTime"></u-cell>
				<u-cell title="驳回时间" :value="searchResult.rejectTime" v-if="searchResult.auditStatus == 3"></u-cell>
			</u-cell-group>
			<view class="qry-action">
				<view class="action-btn">
					<u-button @click="_goBack()" type="info" text="返回"></u-button>
				</view>
				<template v-if="searchResult.auditStatus == 2">
					<view class="action-btn" v-if="searchResult.admissionLetterInStatus == 1">
						<u-button @click="applyNotify" type="success" text="查看录取通知书"></u-button>
					</view>
				</template>
				<view class="action-btn" v-else>
					<template v-if="searchResult.adjustment == 0">
						<u-button type="info" v-if="jumpTp == 1" @click="go2Detail" text="查看报名信息"></u-button>
						<u-button type="primary" v-else-if="jumpTp == 2" @click="go2DetailAndCheckCount" text="修改信息"></u-button>
						<u-button type="primary" v-else-if="jumpTp == 3" @click="go2Edit" text="修改信息"></u-button>
						<u-button type="warning" v-else-if="jumpTp == 4" @click="startOverAgain" text="重新报名"></u-button>
					</template>
					<template v-else-if="searchResult.adjustment == 1">
						<!-- 调剂状态adjustment是1时只能查看报名详情 -->
						<u-button type="info" v-if="jumpTp == 1" @click="go2Detail" text="查看报名信息"></u-button>
					</template>
				</view>
			</view>
		</view>
		<u-toast ref="uToast"></u-toast>
		<u-modal :show="errMsg.show" title="提示" @confirm="errMsg.show = false">
			<view class="slot-content">
				<rich-text :nodes="errMsg.cont"></rich-text>
			</view>
		</u-modal>
	</view>
</template>

<script>
import { idCardReg } from "@/utils/validator.js"
import { qryFirst, searchResById, getNextPath, bindIdCard2CurWx } from "@/apis/admission.js"
export default {
	data() {
		return {
			pageTp: 0,
			// qryFirst参数
			defaultSearch: {
			  key: this.$store.state.userId,
			},
			// 跳转类型
			jumpTp: -1,
			// searchResById参数
			search: {
			  userId: this.$store.state.userId,
			  idCardNumber: '',
			},
			// 通知书参数
			notifySearch: {
			  key: "1",
			},
			// searchResById错误信息
			errorMsg: [
			  "",
			  "请输入有效18位身份证号",
			  "未匹配到学生信息，请前往报名入口进行报名，如有疑问请咨询学校或者教育局",
			  "未匹配到当前用户微信信息",
			  "未匹配到当前学生微信信息",
			  `该学生已通过其他微信报名，微信昵称：`
			],
			// qryFirst结果
			searchResult: {},
			// searchResById结果
			searchResByInput: {
			  studentName: "",
			  studentIdCardNumber: "",
			},
			errMsg: {
				show: false,
				cont: ''
			},
		}
	},
	created() {
		this.$store.commit('SET_APPLY_QRY_RES', {})
		this.$store.commit('SET_SHOW_MODIFY_TP', '')
		this.getDataFirst()
	},
	methods: {
		// 决定按钮和跳转类型
		decideJumpTp() {
			getNextPath({ key: this.$store.state.userId }).then(res => {
				this.jumpTp = res
			})
		},
		// 查询页面显示类型
		getDataFirst() {
		  qryFirst(this.defaultSearch).then((res) => {
		    this.pageTp = res.pageType
				for(let i in res) {
					this.searchResult[i] = res[i]
				}
				// 驳回时检查驳回类型
				if (this.pageTp == 2 && res.auditStatus != 2) {
					this.decideJumpTp()
				}
		  });
		},
		// 根据身份证查询结果
		searchById() {
			if (idCardReg.test(this.search.idCardNumber)) {
				searchResById(this.search).then((res) => {
					// 错误提示
					if (
						res.messageType == 1 ||
						res.messageType == 2 ||
						res.messageType == 3 ||
						res.messageType == 4
					) {
						this.errMsg.cont = this.errorMsg[res.messageType]
						this.errMsg.show = true
					} else if (res.messageType == 5) {
						this.errMsg.cont = this.errorMsg[res.messageType] + res.result.nickname
						this.errMsg.show = true
					} else if (res.messageType == 0) {
						// 查询成功
						// this.errMsg.cont = `学生姓名：${res.result.studentName}<br />身份证号：${res.result.studentIdCardNumber}`
						let params = {
							nm: res.result.studentName,
							idCard: res.result.studentIdCardNumber,
							stuId: res.result.studentId
						}
						uni.navigateTo({
							url: './confirmBind',
							success (r) {
								r.eventChannel.emit('acceptDataFromOpenerPage', params)
							}
						})
					}
				})
			}
		},
		// 查看报名信息
		go2Detail() {
			this.$store.commit('SET_APPLY_QRY_RES', this.searchResult)
			this.$store.commit('SET_SHOW_MODIFY_TP', 1)
			uni.navigateTo({
				url: `/pages/ad-form/detail`
			})
		},
		// 查看报名详情并修改信息
		go2DetailAndCheckCount() {
			this.$store.commit('SET_APPLY_QRY_RES', this.searchResult)
			this.$store.commit('SET_SHOW_MODIFY_TP', 2)
			uni.navigateTo({
				url: `/pages/ad-form/detail`
			})
		},
		// 直接去修改信息
		go2Edit() {
			this.$store.commit('SET_APPLY_QRY_RES', this.searchResult)
			uni.navigateTo({
				url: `/pages/ad-form/edit`
			})
		},
		// 驳回重新报名
		startOverAgain() {
			uni.reLaunch({
				url: `/pages/index/index`
			})
		},
		// 录取通知书
		applyNotify() {
			this.$store.commit('SET_APPLY_QRY_RES', this.searchResult)
			uni.navigateTo({
				url: `/pages/apply-qry/applyCert`
			})
		},
		// 预览二维码
		previewQRCode() {
			if (this.searchResult.qrcodeImage) {
				uni.previewImage({
					urls: [this.searchResult.qrcodeImage],
					current: this.searchResult.qrcodeImage
				})
			}
		},
	}
}
</script>

<style scoped lang="scss">
.apply-qry {
	padding: 10px;
	.qry-action {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 20px;
		.action-btn {
			flex: 0 0 30%;
			margin: 0 5%;
		}
	}
	::v-deep .u-cell__title-text {
		width: 120px;
	}

	.qrcode-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 10px 0;

		.qrcode-image {
			width: 200px;
			height: 200px;
			border: 1px solid #e4e7ed;
			border-radius: 8px;
			cursor: pointer;
		}

		.qrcode-tip {
			margin-top: 8px;
			font-size: 12px;
			color: #909399;
		}
	}
}
</style>