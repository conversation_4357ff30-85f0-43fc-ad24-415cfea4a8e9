import store from '@/store'

// 接口前缀,由 “pref+区县deptCode” 组成,例 /shida-region-biz-service-130406/xxx/...
const pref = '/tshgzs-region-biz-service-'

const roleOptions = [
  {
    label: '区县教育局管理员',
    value: '2'
  },
  {
    label: '区县教育局审核员',
    value: '3'
  },
  {
    label: '学校管理员',
    value: '4'
  },
  {
    label: '公安部门',
    value: '6'
  },
  {
    label: '房管部门',
    value: '7'
  }
]
const schoolTypeOptions = [
  {
    label: '小学',
    value: '2'
  },
  {
    label: '初中',
    value: '3'
  }
]

// 图片前缀
const imgPrefix = () => {
	let prefix = `${ process.env.VUE_APP_BASE_API }${ pref }${ store.state.deptCode }`
	return prefix
}

const baseUrl = 'wx-lunan'

// 设置报名数据缓存
// 参数val接受格式：{报名入口id: []}，传空对象则清空缓存。
// 例如: {74: [originList]} 即设置入口id为74的报名数据
// 例如: {}即清空所有报名数据缓存
// 用vuex老报错，所以直接操作uni.storage
const setCacheData4Ad = (val) => {
	// 如果是空
	if (JSON.stringify(val) == '{}') {
		// 清空本对象
		uni.removeStorageSync('cacheData4Ad')
	} else {
		// 接收到的值的键
		let k = Object.keys(val)[0]
		// 接收到的值的值
		let v = Object.values(val)[0]
		let originCache = uni.getStorageSync('cacheData4Ad')
		// 如果还没创建storage
		if (!originCache) {
			let cache = JSON.stringify(val)
			uni.setStorageSync('cacheData4Ad', cache)
		} else {
			// console.log(originCache, JSON.parse(originCache))
			 // 已创建storage, 更新对应键
			let parseObj = JSON.parse(originCache)
			// console.log('操作方法内，取出的缓存为', parseObj)
			// 赋值
			parseObj[k] = v
			uni.setStorageSync('cacheData4Ad', JSON.stringify(parseObj))
		}
		// console.log('操作方法内，缓存已更新，最新的数据为', JSON.parse(uni.getStorageSync('cacheData4Ad')))
	}
}

// 获取报名数据缓存
const getCacheData4Ad = (k) => {
	let originCache = uni.getStorageSync('cacheData4Ad')
	// 有缓存
	if (originCache) {
		let parseCache = JSON.parse(originCache)
		// console.log(parseCache)
		// 有这个键
		if (parseCache.hasOwnProperty(k) && JSON.stringify(parseCache[k]) != '{}') {
			// console.log(JSON.parse(parseCache[k]))
			return JSON.parse(parseCache[k])
		} else {
			// 否则为空
			return {}
		}
	} else {
		// 否则为空
		return {}
	}
}

export {
  pref,
  roleOptions,
  schoolTypeOptions,
	imgPrefix,
	baseUrl,
	setCacheData4Ad,
	getCacheData4Ad
}