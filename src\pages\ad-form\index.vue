<template>
  <view class="entrance">
    <u-skeleton rows="10" :loading="loadingAdForm">
      <u-alert :title="`当前报名学校：${ adSchool.deptName }`" type="success" effect="dark"></u-alert>
      <u-form :model="form" :rules="rules" ref="form" labelPosition="top" errorType="toast" labelWidth="auto">
        <!-- 非房产非双胞胎的普通字段 -->
        <view v-for="item, idx in normalForm" :key="idx" class="form-group">
          <u-alert :title="item.infoName" type="info"></u-alert>
          <view class="f-group-detail">
            <template v-for="fi, fidx in item._normalItem">
              <u-form-item :prop="`${ fi.fieldId }`" :label="`${ fi.fieldName }`" :required="fi.isNecessary == 1">
                <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange"
                              v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange"
                               v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
              </u-form-item>
            </template>
            <template v-for="fi, fidx in item._imgItem">
              <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1" >
                <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
              </u-form-item>
            </template>
          </view>
        </view>
        <!-- 双胞胎 -->
        <view class="form-group" v-if="siblings.allList.length > 0">
          <view class="fake-title">
            <text>双胞胎</text>
            <u-switch v-model="siblings.isHaveSib" @change="initOrHideSib"></u-switch>
          </view>
          <view class="f-group-detail">
            <view class="sibs-list">
              <view class="sibs-item" v-for="si, sIdx in siblings.list" :key="sIdx">
                <view class="f-g-sub-title">双胞胎{{ sIdx + 1 }}</view>
                <template v-for="fi, fidx in si._normalItem">
                  <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
                    <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange"
                                  v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                    <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange"
                                   v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                  </u-form-item>
                </template>
                <template v-for="fi, fidx in si._imgItem">
                  <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
                    <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi"
                                       @value-change="uploadValueChange"></normal-img-upload>
                  </u-form-item>
                </template>
              </view>
            </view>
            <view class="sib-actions">
              <u-button type="primary" class="sib-actions-btn add" text="添加" @click="sibAdd"
                        v-if="siblings.list.length == 1"></u-button>
              <u-button type="error" class="sib-actions-btn del" text="删除" @click="sibDel"
                        v-if="siblings.list.length == 2"></u-button>
            </view>
          </view>
        </view>
        <!-- 随迁 -->
        <view class="form-group" v-if="sq.isSQ">
          <view class="fake-title">
            <text>{{ sq.title }}</text>
            <view class="sq-selector">
              <u-subsection @change="sqTpChange" :list="sq.selector" keyName="name" :current="sq.tpIdx"></u-subsection>
            </view>
          </view>
          <view class="f-group-detail">
            <view v-for="sqItem, sqIdx in sq.list" :key="sqItem.typeConfigId">
              <view v-for="fi, fidx in sqItem._normalItem" :key="fi.fieldId">
                <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
                  <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange"
                                v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                  <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange"
                                 v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                </u-form-item>
              </view>
              <view v-for="fi, fidx in sqItem._imgItem" :key="fi.fieldId">
                <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
                  <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi"
                                     @value-change="uploadValueChange"></normal-img-upload>
                </u-form-item>
              </view>
            </view>
          </view>
        </view>
        <!-- 房产 -->
        <view class="form-group" v-if="propertyForm.list.length > 0">
          <u-alert title="房产信息" type="info"></u-alert>
          <view class="normal-select fixed-type" @click="openSelector('propertyForm')">
            <u-action-sheet :actions="propertyForm.selector" title="房产类型" :show="propertyForm.showSelect"
                            @select="ppTabChange" @close="closeSelector('propertyForm')"
                            :closeOnClickOverlay="false"></u-action-sheet>
            <view class="ph-txt">房产类型</view>
            <view class="fake-input">
              <view class="res-txt">{{ propertyForm.tpCn }}</view>
              <text class="fake-input-icon">
                <u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
              </text>
            </view>
          </view>
          <view class="f-group-detail">
            <view v-for="ppItem, ppIdx in propertyForm.curList" :key="ppItem.typeConfigId">
              <view v-for="fi, fidx in ppItem._normalItem" :key="fi.fieldId">
                <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
                  <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange"
                                v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                  <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange"
                                 v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                </u-form-item>
                <p v-if="fi.fieldName==='房屋所有权人姓名'" class="label-text">房屋所有权人可填写多位，用逗号分隔</p>
                <p v-if="fi.fieldName==='身份证号'"  class="label-text">请提供与学生有亲子关系的所有权人身份证号码</p>
              </view>
              <view v-for="fi, fidx in ppItem._imgItem" :key="fi.fieldId">
                <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
                  <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi"
                                     @value-change="uploadValueChange"></normal-img-upload>
                </u-form-item>
              </view>
            </view>
          </view>
        </view>

        <!-- 优抚对象 -->
        <view class="form-group" v-if="youFuForm.list.length > 0">
          <u-alert title="优抚对象" type="info"></u-alert>
          <view class="normal-select fixed-type" @click="openSelector('youFuForm')">
            <u-action-sheet :actions="youFuForm.selector" title="优抚类型" :show="youFuForm.showSelect" @select="yfTabChange" @close="closeSelector('youFuForm')" :closeOnClickOverlay="false"></u-action-sheet>
            <view class="ph-txt"><text class="pp-tp-required">*</text>优抚类型</view>
            <view class="fake-input">
              <view class="res-txt">{{ youFuForm.tpCn }}</view>
              <text class="fake-input-icon">
                <u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
              </text>
            </view>
          </view>
          <view class="f-group-detail">
            <view v-for="ppItem, ppIdx in youFuForm.curList" :key="ppItem.typeConfigId">
              <view v-for="fi, fidx in ppItem._normalItem" :key="fi.fieldId">
                <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
                  <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                  <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                </u-form-item>
              </view>
              <view v-for="fi, fidx in ppItem._imgItem" :key="fi.fieldId">
                <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
                  <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
                </u-form-item>
              </view>
            </view>
          </view>
        </view>

        <view class="form-group" v-if="others.list.length > 0">
          <u-alert title="其他材料证明" type="info"></u-alert>
          <view class="f-group-detail">
            <view v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId">
              <template v-for="fi, fidx in oItem._normalItem">
                <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
                  <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange"
                                v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                  <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange"
                                 v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                </u-form-item>
              </template>
              <template v-for="fi, fidx in oItem._imgItem">
                <u-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" :required="fi.isNecessary == 1">
                  <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi"
                                     @value-change="uploadValueChange"></normal-img-upload>
                </u-form-item>
              </template>
            </view>
          </view>
        </view>

      </u-form>
      <u-button @click="submitAdForm" text="提交" :disabled="submitDisable"></u-button>
    </u-skeleton>
    <u-modal :show="adSubmitModal.show" :title="adSubmitModal.title" :content='adSubmitModal.content'
             @confirm="back2Home"></u-modal>
  </view>
</template>

<script>
import {adFormDetail, submitAd, submitAdSecond, getNextPath} from '@/apis/admission'
import NormalInput from "@/components/Form/NormalInput"
import NormalSelect from "@/components/Form/NormalSelect"
import NormalImgUpload from "@/components/Form/NormalImgUpload"
import {LoopFn} from "@/mixins/loopFn"
import {EntitledGroupType} from "@/utils/bizDict";
import EasySelect from "@/components/easy-select/easy-select.vue";
import {registrationCategories} from "@/utils/bizUtil";

export default {
  components: {
    EasySelect,
    NormalInput,
    NormalSelect,
    NormalImgUpload
  },
  mixins: [LoopFn],
  data() {
    return {
      entryId: this.$store.state.entry3,
      adSchool: this.$store.state.schoolDetail,
      // 是否已加载缓存
      isLoadCache: false,
      entitledGroupType: EntitledGroupType,
      form: {
        type: 0
      },
      tempValue: '现役军人子女',
      entitledGroupFlag: false
    }
  },
  created() {
    // 是否随迁类型
    this.sq.isSQ = this.sq.idList.some(v => v == this.entryId)
    this.getData()
    if (registrationCategories.isEntitledGroup(this.entryId)) {
      this.entitledGroupFlag = true;
    }
  },
  // mounted可以，但只能存一次
  mounted() {
    this.$nextTick(() => {
      // 尚未加载缓存
      if (!this.isLoadCache) {
        // 检查是否有缓存
        let cacheData = this.$getCacheData4Ad(this.entryId)
        // 当前报名类型的缓存不为空
        if (cacheData && JSON.stringify(cacheData) != '{}') {
          // console.log('加载缓存', cacheData)
          // 将缓存更新至页面
          this.processFormObj(JSON.parse(JSON.stringify(cacheData)), true)
        }
      } else {
        // console.log('不走加载缓存了', this)
      }
    })
  },
  methods: {
    // 获取表单
    getData() {
      adFormDetail({
        key: this.entryId
      }).then(res => {
        let resCopy = JSON.parse(JSON.stringify(res))
        this.processFormObj(resCopy)
      })
    },
    // 拿到表单后的处理
    // res: 表单数据，isCache: 是否是缓存数据
    processFormObj(res, isCache) {
      this.loadingAdForm = true
      // 按typeConfigId从小到大排序并分类
      this.originList = res.sort((a, b) => a.typeConfigId - b.typeConfigId).map(this.separateImgAndNormal)

      // 是随迁
      if (this.sq.isSQ) {
        // 排除经商5和务工6
        let normalIdList = [1, 2, 4, 7].map(v => `${v}`)
        this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
        this.sq.allList = this.originList.filter(v => v.typeConfigId == 5 || v.typeConfigId == 6)
        // 如果配置了经商务工
        if (this.sq.allList.length > 0) {
          this.sq.selector = this.sq.allList.map(v => {
            return {
              name: v.infoName,
              id: v.typeConfigId
            }
          })
          let selected = this.sq.allList[0].typeConfigId
          this.sq.tp = this.sq.selector.findIndex(v => v.id == selected)
          this.sqTpChange(this.sq.tp)
        }
      } else {
        let normalIdList = [1, 2, 4, 5, 6, 7, 25].map(v => `${v}`)
        this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
      }


      //优抚信息字段 20-24
      this.youFuForm.list = this.originList.filter(v => v.typeConfigId >= 20 && v.typeConfigId < 25)
      if (this.youFuForm.list.length > 0) {
        // 初始化优抚选择器
        this.youFuForm.selector = this.youFuForm.list.map(v => {
          return {
            name: v.infoName,
            id: v.typeConfigId
          }
        })
        this.youFuForm.tpCn = '请选择优抚类型'
      }
      // 设置默认值
      if (this.youFuForm.tp && this.youFuForm.selector.length > 0) {
        // 安全地查找选中的优抚类型
        const selectedYouFu = this.youFuForm.selector.find(v => v.id == this.youFuForm.tp);
        if (selectedYouFu) {
          this.youFuForm.tpCn = selectedYouFu.name;
        }
      }
      // 房产字段：typeConfigId >= 8但小于18
      this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 && v.typeConfigId < 18)
      // 双胞胎字段：typeConfigId为3和19
      this.siblings.allList = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
      // 其他补充信息
      this.others.list = this.originList.filter(v => v.typeConfigId == 18)
      if (this.others.list.length > 0) {
        this.others.list.map(this.addValidRules)
      }
      // 如果有房产
      if (this.propertyForm.list.length > 0) {
        // 初始化房产选择器
        this.propertyForm.selector = this.propertyForm.list.map(v => {
          return {
            name: v.infoName,
            id: v.typeConfigId
          }
        })
        // 房产默认选中第1个tab
        this.propertyForm.tp = this.propertyForm.list[0].typeConfigId
        this.propertyForm.lastTp = this.propertyForm.tp
        // 当前选中类型中文
        this.propertyForm.tpCn = this.propertyForm.selector.find(v => v.id == this.propertyForm.tp).name
        // 当前选中类型表单
        this.propertyForm.curList = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp)
        // 添加第1个tab的验证规则
        this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp).map(this.addValidRules)
      }
      this.loadingAdForm = false
      // 如果传进来的是缓存
      if (isCache) {
        // 标识置为已加载完成
        this.isLoadCache = true
        // console.log('缓存加载完了', this)
      }
    },
    // 提交
    submitAdForm() {
      this.submitDisable = true
      let params = {
        setUpSaveIds: this.entryId,
        enrollSchoolId: this.adSchool.id,
        enrollSchoolName: this.adSchool.deptName,
        userId: this.$store.state.userInfo.id,
        enrollMiddleFieldFormList: this.originList,
        careType: this.youFuForm.tp,
        houseType: this.propertyForm.tp
      }
      // 没选双胞胎就去掉上传字段
      if (!this.siblings.isHaveSib) {
        params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 3 && v.typeConfigId != 19)
      }
      // 是随迁传入选中的随迁类型
      if (this.sq.isSQ) {
        params.followWorkType = this.sq.tp
      }

      // 优抚类型配置相关字段
      if (this.entitledGroupFlag) {
        const tempGroup = {
          ...this.entitledGroupType[this.form.type],
          fields: this.entitledGroupType[this.form.type].fields.map(item => ({...item}))
        };
        tempGroup.fields.forEach(item => {
          item.value = this.form[item.prop];
        });
        // 赋值给参数对象
        params.entitledGroupForm = {
          ...tempGroup,
          fields: JSON.stringify(tempGroup.fields)
        };
      }
      console.log("form", params);

      this.$refs['form'].validate().then(async valid => {
        // 查询当前学生是否报过名
        let isStuAd = await getNextPath({key: this.$store.state.userId})
        if (isStuAd == 0) {
          submitAd(params).then(res => {
            this.adSubmitModal.content = '报名成功'
            this.submitDisable = false
            this.adSubmitModal.show = true
            this.$setCacheData4Ad({})
          }).catch(err => {
            this.submitDisable = false
          })
        } else {
          submitAdSecond(params).then(res => {
            this.adSubmitModal.content = '修改报名成功'
            this.submitDisable = false
            this.adSubmitModal.show = true
          }).catch(err => {
            this.submitDisable = false
          })
        }
      }).catch(validErr => {
        this.submitDisable = false
      })
    },
    selectGroup(options) {
      let {label,value,fields} = options
      this.tempValue = label;
      this.form.type = value;
    },
  }
}
</script>

<style lang="scss" scoped>

.label-text{
  color: #909399;
  font-size: 12px;
  margin-bottom: 5px;
}
.entrance {
  padding: 10px;

  .form-group {
    margin-top: 10px;
  }

  .fake-title {
    position: relative;
    padding: 8px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    color: #909399;
    border-radius: 4px;
    background-color: #f4F4F5;
  }

  .sq-selector {
    width: 50%;
  }

  .sibs-item {
    padding: 10px;
    border-bottom: 1px dashed #dcdfe6;
  }

  .sib-actions {
    margin-top: 10px;

    .sib-actions-btn {
      width: 30%;
    }
  }

  .f-g-sub-title {
    font-style: italic;
    color: #CCC;
  }

  .fixed-type {
    margin-bottom: 10px;

    .ph-txt {
      margin-top: 15px;
      margin-bottom: 5px;
      font-size: 15px;
    }
  }
}
</style>