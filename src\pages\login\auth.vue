<template>
	<view class="auth">
		<view>正在获取微信授权，请稍后</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			auth: {
        code: '',
        state: ''
      }
		}
	},
	created() {
		this.auth.code = this.$route.query.code
		this.auth.state = this.$route.query.state
    this.login()
	},
	methods: {
    login() {
      this.$store.dispatch('login', this.auth).then(res => {
				this.$store.commit('setLoginTp', 'wx')
				uni.redirectTo({
					url: '../index/index'
        })
      })
    }
	}
}
</script>

<style lang='scss' scoped>
.auth {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
</style>