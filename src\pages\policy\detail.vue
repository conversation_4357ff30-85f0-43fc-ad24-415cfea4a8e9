<template>
	<view class="policy-detail">
		<view class="policy-h">{{ title }}</view>
		<u-parse :content="content"></u-parse>
	</view>
</template>

<script>
import { policyDetail } from '@/apis/other'
export default {
	data() {
		return {
			id: '',
			title: '',
			content: ''
		}
	},
	onLoad() {
		let that = this
		// #ifdef APP-NVUE
		const evtChanel = that.$scope.eventChannel
		// #endif
		// #ifndef APP-NVUE
		const evtChanel = that.getOpenerEventChannel()
		// #endif
		evtChanel.on('acceptDataFromOpenerPage', function (data) {
			that.id = data.id
			that.getData()
		})
	},
	methods: {
		getData() {
			policyDetail({
				key: this.id
			}).then(res => {
				this.title = res.title
				this.content = res.content
			})
		}
	}
}
</script>

<style scoped lang="scss">
.policy-detail {
	padding: 15px;
	padding-top: 0;
	.policy-h {
		font-size: 18px;
		margin-bottom: 10px;
		text-align: center;
	}
}
</style>