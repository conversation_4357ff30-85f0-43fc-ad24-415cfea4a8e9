import Vue from 'vue'
import App from './App'
import './uni.promisify.adaptor'
// 使用uview
import uView from "uview-ui"
// 引入vuex
import store from '@/store'
// 加载apis
import apis from '@/apis'
// 路由守卫
import Permissions from '@/permissions'
// 公共返回方法
import { GoBack } from './mixins/GoBack.js'

import { setCacheData4Ad, getCacheData4Ad } from '@/utils/common.js'

Vue.mixin(GoBack)

Vue.use(uView);
Vue.use(apis);
Vue.use(Permissions);

Vue.prototype.$setCacheData4Ad = setCacheData4Ad
Vue.prototype.$getCacheData4Ad = getCacheData4Ad

Vue.config.productionTip = false
App.mpType = 'app'

const app = new Vue({
  store,
  ...App
})

app.$mount()