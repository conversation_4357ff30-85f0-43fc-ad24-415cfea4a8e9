<template>
  <view class="normal-select">
    <!-- 日期选择：verCode = 2 or 14" -->
    <template v-if="verCode == 2 || verCode == 14">
      <u-datetime-picker
          mode="date"
          v-model="modelData"
          :show="show"
          :minDate="minDate"
          :maxDate="maxDate"
          @confirm="dateValChange"
          @cancel="hidePicker"
      ></u-datetime-picker>
      <!-- 模拟输入框样式 -->
      <view class="fake-input u-input u-input--square" @click="showPicker">
        <view class="res-txt" v-if="modelData">{{ dateFmt }}</view>
        <view class="ph-txt" v-else>{{ phTxt }}</view>
        <view class="fake-input-icon">
          <u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
        </view>
      </view>
    </template>

    <template v-else-if="verCode === 19">
      <!-- 模拟输入框样式 -->
      <view class="fake-input u-input u-input--square" @click="showAreaPicker1">
        <view class="res-txt" v-if="areaModel.province">{{ areaDisplayText }}</view>
        <view class="ph-txt" v-else>{{ phTxt }}</view>
        <view class="fake-input-icon">
          <u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
        </view>
      </view>

      <!-- 省市区选择器 -->
      <u-picker
          ref="areaPicker"
          :show="showAreaPicker"
          :columns="areaColumns"
          title="请选择省市区"
          keyName="label"
          @confirm="confirmArea"
          @cancel="cancelArea"
          @change="areaChangeHandler"
      ></u-picker>
    </template>

    <!-- 其它selector -->
    <template v-else>
      <view class="fake-input" @click="openSelector">
        <view class="res-txt" v-if="modelData" :a-model-data="modelData">{{ modelCn }}</view>
        <view class="ph-txt" v-else>{{ phTxt }}</view>
        <view class="fake-input-icon">
          <u-icon name="arrow-down" color="#dadbde" size="14px"></u-icon>
        </view>
      </view>
      <u-popup :show="show" :closeOnClickOverlay="false">
        <view class="selector-wrap">
          <view class="selector-title">
            <text class="s-t-btn s-t-cancel" @click="cancelChoose">取消</text>
            <text class="s-t-btn s-t-confirm" @click="confirmChoose" v-show="selectIdx != -1">确定</text>
          </view>
          <view class="selector-list">
            <view class="selector-item" :ref="`${ itemConfig.fieldId }-${ idx }`" v-for="item, idx in dataSource" :key="idx" @click="choose(idx)" :class="{ 'item-selected': selectIdx == idx }">
              <view class="res-txt">{{ item.val }}</view>
              <view class="select-icon">
                <u-icon name="checkmark" v-if="selectIdx == idx" color="#FFF"></u-icon>
              </view>
            </view>
          </view>
        </view>
      </u-popup>
    </template>
  </view>
</template>

<script>
import { rulesList } from "@/utils/dictionary"
import {schoolList, schoolAdRange} from "@/apis/admission.js"
import areas from '@/utils/areas.json'; // 导入省市区数据
export default {
  name: 'normal-select',
  components: {
  },
  data() {
    return {
      show: false,
      // 实际后台需要的值
      modelData: '',
      // id对应的中文值
      modelCn: '',
      // 非日期类型的选中下标
      selectIdx: -1,
      phTxt: '',
      minDate: Number(new Date(`${ (new Date()).getFullYear() - 120 }-01-01`)),
      maxDate: Number(new Date(`${ (new Date()).getFullYear() + 15 }-01-01`)),
      applyRes: this.$store.state.applyQryResult,
      dataSource: [],
      isFilterable: false,
      // 新增省市区相关数据
      showAreaPicker: false,
      areaColumns: [[], [], []], // 省、市、区三列
      areaModel: {
        province: null, // 选中的省ID
        city: null,     // 选中的市ID
        district: null  // 选中的区ID
      },
      areaData: [],     // 完整的省市区数据
      areaText: {       // 存储选中的省市区文本
        province: '',
        city: '',
        district: ''
      }
    }
  },
  props: {
    // 字段所有配置
    itemConfig: {
      type: Object,
      required: true
    }
  },
  computed: {
    // 验证规则的快捷方式
    verCode() {
      return this.itemConfig.infoVerificationCode
    },
    // 日期格式化
    dateFmt() {
      return uni.$u.timeFormat(this.modelData, 'yyyy-mm-dd')
    },
    areaDisplayText() {
      return `${this.areaText.province} ${this.areaText.city} ${this.areaText.district}`.trim()
    }
  },
  watch: {
    'itemConfig.fieldValue': {
      handler(newV, oldV) {
        if (newV && newV != oldV) {
          // 如果有值，直接填充
          if (this.itemConfig.fieldValue) {
            this.modelData = this.itemConfig.fieldValue
            if (this.verCode == 2 || this.verCode == 14) {
              this.dateValChange(this.itemConfig.fieldValue)
            }
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.phTxt = `请选择${ this.itemConfig.fieldName }`
    // inputItemCode为6时可以输入匹配
    this.isFilterable = this.itemConfig.inputItemCode == 6
    // infoVerificationCode为3，4，5，9，10，12，13时直接读取字典里的list
    let dicListIdx = [3, 4, 5, 9, 10, 12, 13,16,17,18]
    // infoVerificationCode为678时需要请求api获取数据
    let qryListIdx = [6, 7, 8 ,19]
    if (dicListIdx.indexOf(this.verCode) != -1) {
      this.dataSource = rulesList[this.verCode].list
      this.selectIdx = this.dataSource.findIndex(v => v.id == this.modelData)
      this.pickerChange({}, this.modelData)
    } else if (qryListIdx.indexOf(this.verCode) != -1) {
      if (this.verCode == 6) {
        this.primarySchoolList()
      } else if (this.verCode == 7) {
        this.preSchoolList()
      } else if (this.verCode == 8) {
        this.rangeList()
      }else if (this.verCode === 19) {
        // 省市区数据处理
        this.initAreaData()
      }
    }
  },
  methods: {
    initAreaData() {
      this.areaData = areas // 使用导入的省市区数据

      // 初始化省份数据
      this.areaColumns[0] = this.areaData.map(province => ({
        label: province.label,
        value: province.value
      }))

      // 如果有初始值，设置选中状态
      if (this.itemConfig.fieldValue) {
        this.setInitialAreaValue(this.itemConfig.fieldValue)
      }
    },
    setInitialAreaValue(value) {
      // 假设初始值格式为 "省ID,市ID,区ID"
      const [provinceVal, cityVal, districtVal] = value.split(',').map(Number)

      // 查找对应的省
      const province = this.areaData.find(p => p.value === provinceVal)
      if (province) {
        this.areaModel.province = province.value
        this.areaText.province = province.label

        // 查找对应的市
        if (cityVal && province.children) {
          const city = province.children.find(c => c.value === cityVal)
          if (city) {
            this.areaModel.city = city.value
            this.areaText.city = city.label

            // 更新市级数据
            this.areaColumns[1] = province.children.map(c => ({
              label: c.label,
              value: c.value
            }))

            // 查找对应的区
            if (districtVal && city.children) {
              const district = city.children.find(d => d.value === districtVal)
              if (district) {
                this.areaModel.district = district.value
                this.areaText.district = district.label

                // 更新区级数据
                this.areaColumns[2] = city.children.map(d => ({
                  label: d.label,
                  value: d.value
                }))
              }
            }
          }
        }
      }
    },
    showAreaPicker1() {
      this.showAreaPicker = true
    },
    confirmArea(e) {
      const [province, city, district] = e.value

      // 更新模型数据
      this.areaModel = {
        province: province?.value || null,
        city: city?.value || null,
        district: district?.value || null
      }

      // 更新显示文本
      this.areaText = {
        province: province?.label || '',
        city: city?.label || '',
        district: district?.label || ''
      }

      // 拼接完整值并触发change事件
      const fullValue = [
        this.areaModel.province,
        this.areaModel.city,
        this.areaModel.district
      ].filter(v => v !== null).join(',')

      this.valChange(fullValue)
      this.showAreaPicker = false
    },
    cancelArea() {
      this.showAreaPicker = false
    },
    areaChangeHandler(e) {
      const { columnIndex, index } = e
      const picker = this.$refs.areaPicker

      // 省变化
      if (columnIndex === 0) {
        const province = this.areaData[index]

        // 更新市级数据
        this.areaColumns[1] = province?.children?.map(city => ({
          label: city.label,
          value: city.value
        })) || []
        picker.setColumnValues(1, this.areaColumns[1])

        // 重置市和区选择
        this.areaColumns[2] = []
        picker.setColumnValues(2, [])
      }
      // 市变化
      else if (columnIndex === 1) {
        const provinceIndex = picker.getIndexs()[0]
        const province = this.areaData[provinceIndex]
        if (province?.children?.[index]) {
          const city = province.children[index]

          // 更新区级数据
          this.areaColumns[2] = city?.children?.map(district => ({
            label: district.label,
            value: district.value
          })) || []
          picker.setColumnValues(2, this.areaColumns[2])
        }
      }
    },
    // 显示日期
    showPicker() {
      this.show = true
    },
    // 关闭日期
    hidePicker() {
      this.show = false
    },
    // 打开并匹配选项
    openSelector() {
      // 当存在已选中值和弹窗内高亮不一致的情况时，将弹窗内的高亮改成modelData值
      if (this.modelData) {
        this.selectIdx = this.dataSource.findIndex(v => v.id == this.modelData)
      }
      this.showPicker()
    },
    // 选择
    choose(idx) {
      this.selectIdx = idx
    },
    // 取消选择
    cancelChoose() {
      this.hidePicker()
      // 字段没值，打开弹窗选择任意选项然后后点击取消，这时modelData还是空的
      if (!this.modelData) {
        // 所以选中下标还是-1
        this.selectIdx = -1
      }
    },
    // 确认选择
    confirmChoose() {
      let matched = this.dataSource[this.selectIdx]
      this.pickerChange({}, matched.id)
      this.hidePicker()
    },
    // 普通picker的change
    pickerChange(e, data) {
      if (data) {
        // 直接按id找到对应值
        let idx = this.dataSource.findIndex(v => v.id == data)
        this.selectIdx = idx
        this.modelData = this.dataSource[idx].id
        this.modelCn = this.dataSource[idx].val
        this.valChange(this.modelData)
      } else if (e && e.target) {
        // picker手动选择
        let idx = e.target.value
        this.selectIdx = idx
        this.modelData = this.dataSource[idx].id
        this.modelCn = this.dataSource[idx].val
        this.valChange(this.modelData)
      } else {
        // 初始状态，空值
        this.modelData = ''
        this.modelCn = ''
        this.selectIdx = -1
        this.valChange('')
      }
    },
    // 日期change
    dateValChange(data) {
      let retVal = ''
      // 手动选择
      if (data.mode == 'date' && typeof data.value == 'number') {
        retVal = uni.$u.timeFormat(data.value, 'yyyy-mm-dd')
      } else {
        // 父级组件直接访问并修改值
        retVal = data
      }
      this.modelData = retVal
      this.valChange(retVal)
      this.hidePicker()
    },
    // emit
    valChange(data) {
      this.$emit('value-change', {
        id: this.itemConfig.fieldId,
        val: data
      })
    },
    // infoVerificationCode == 6时，小学列表
    primarySchoolList() {
      /*let params = {
        keywords: "",
        nature: '',
        // 学段
        period: 2,
        deptCode: this.$store.state.deptCode,
        type: 1,
        pageNumber: 1,
        pageSize: 9999
      }*/
      let params ={}
      if(this.$store.state.entry4!==''){
        params = {
          keywords: "",
          nature: '',
          // 学段
          period: 2,
          type: this.$store.state.entry4,
          deptCode: this.$store.state.deptCode,
          pageNumber: 1,
          pageSize: 9999
        }
      }else{
        params = {
          keywords: "",
          nature: '',
          // 学段
          period: 2,
          deptCode: this.$store.state.deptCode,
          pageNumber: 1,
          pageSize: 9999
        }
      }
      schoolList(params).then(res => {
        res.records.forEach(v => {
          v.val = v.deptName
        })
        this.dataSource = res.records
        // 修改报名页面，手动刷新对应select值
        if (this.modelData) {
          this.selectIdx = this.dataSource.findIndex(v => v.id == this.modelData)
          this.pickerChange({}, this.modelData)
        }
      })
    },
    // infoVerificationCode == 7时，幼儿园列表
    preSchoolList() {
      let params = {
        keywords: "",
        nature: '',
        // 学段
        period: 1,
        deptCode: this.$store.state.deptCode,
        type: 1,
        pageNumber: 1,
        pageSize: 9999
      }
      schoolList(params).then(res => {
        res.records.forEach(v => {
          v.val = v.deptName
        })
        this.dataSource = res.records
        // 同上
        if (this.modelData) {
          this.selectIdx = this.dataSource.findIndex(v => v.id == this.modelData)
          this.pickerChange({}, this.modelData)
        }
      })
    },
    // infoVerificationCode == 8时，招生范围列表
    rangeList() {
      let params = {
        type: 2
      }
      // 如果已有报名学校，取报名学校id
      if (this.applyRes.enrollSchoolId) {
        params.schoolId = this.applyRes.enrollSchoolId
      } else {
        // 否则取已选择的学校id
        params.schoolId = this.$store.state.schoolDetail.id
      }
      schoolAdRange(params).then(res => {
        res.forEach(v => {
          v.val = v.rangeName
        })
        this.dataSource = res
        // 同上
        if (this.modelData) {
          this.selectIdx = this.dataSource.findIndex(v => v.id == this.modelData)
          this.pickerChange({}, this.modelData)
        }
      })
    },

  }
}
</script>

<style>
/* 样式在index.scss里 */
</style>