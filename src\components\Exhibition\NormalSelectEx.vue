<template>
	<view class="normal-select-ex">
		<text>{{ modelData }}</text>
	</view>
</template>

<script>
import { rulesList } from "@/utils/dictionary"
import { schoolList, schoolAdRange } from "@/apis/admission.js"
import areas from "@/utils/areas.json"
export default {
	name: 'normal-select-ex',
	data() {
		return {
			modelData: '',
			fieldVal: '',
			applyRes: this.$store.state.applyQryResult,
			areaData: [] // 省市区数据
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		}
	},
	computed: {
		// 验证规则的快捷方式
		verCode() {
			return this.itemConfig.infoVerificationCode
		}
	},
	created() {
		this.fieldVal = this.itemConfig.fieldValue
		this.modelData = this.fieldVal
		// infoVerificationCode为3，4，5，9，10，12，13时直接读取字典里的list
    let dicListIdx = [3, 4, 5, 9, 10, 12, 13,16]
		// infoVerificationCode为6，7，8时需要请求api获取数据
		let qryListIdx = [6, 7, 8]
		if (dicListIdx.indexOf(this.verCode) != -1) {
			let matchItem = rulesList[this.verCode].list.filter(v => v.id == this.fieldVal)
			if (matchItem.length > 0) {
				this.modelData = matchItem[0].val
			} else {
				this.modelData = ''
			}
		} else if (qryListIdx.indexOf(this.verCode) != -1) {
			if (this.verCode == 6) {
				this.primarySchoolList()
			} else if (this.verCode == 7) {
				this.preSchoolList()
			} else if (this.verCode == 8) {
				this.rangeList()
			}
		} else if (this.verCode === 19) {
			// 省市区数据处理
			this.initAreaData()
		}
	},
	methods: {
		// infoVerificationCode == 6时，小学列表
		primarySchoolList() {
			let params = {
				keywords: "",
				nature: '',
				// 学段
				period: 2,
				deptCode: this.$store.state.deptCode,
				type: 1,
				pageNumber: 1,
				pageSize: 9999
			}
			schoolList(params).then(res => {
				res.records.forEach(v => {
					if (v.id == this.fieldVal) {
						this.modelData = v.deptName
					}
				})
			})
		},
		// infoVerificationCode == 7时，幼儿园列表
		preSchoolList() {
			let params = {
				keywords: "",
				nature: '',
				// 学段
				period: 1,
				deptCode: this.$store.state.deptCode,
				type: 1,
				pageNumber: 1,
				pageSize: 9999
			}
			schoolList(params).then(res => {
				res.records.forEach(v => {
					if (v.id == this.fieldVal) {
						this.modelData = v.deptName
					}
				})
			})
		},
		// infoVerificationCode == 8时，范围列表
		rangeList() {
			let params = {
				type: 2,
				schoolId: this.applyRes.enrollSchoolId
			}
			schoolAdRange(params).then(res => {
				res.forEach(v => {
					if (v.id == this.fieldVal) {
						this.modelData = v.rangeName
					}
				})
			})
		},
		// 省市区数据处理
		initAreaData() {
			// 初始化省市区数据
			this.areaData = areas

			if (!this.fieldVal) {
				this.modelData = ''
				return
			}

			// 查找省市区数据
			const areaText = this.findAreaText(this.fieldVal)
			this.modelData = areaText || this.fieldVal
		},

		// 根据区域代码查找对应的文本
		findAreaText(areaCode) {
			if (!areaCode || !this.areaData) {
				return null
			}

			// 处理多个区域代码的情况（用逗号分隔）
			if (typeof areaCode === 'string' && areaCode.includes(',')) {
				const areaCodes = areaCode.split(',').map(code => code.trim())
				// 智能组合省市区信息
				return this.combineAreaInfo(areaCodes)
			}

			return this.findSingleAreaText(areaCode)
		},

		// 智能组合省市区信息
		combineAreaInfo(areaCodes) {
			const areaInfo = {
				province: null,
				city: null,
				district: null
			}

			// 遍历所有区域代码，找到对应的省市区信息
			for (let code of areaCodes) {
				const numericCode = parseInt(code)
				
				// 遍历省份
				for (let province of this.areaData) {
					if (province.value === numericCode) {
						areaInfo.province = province.label
						break
					}

					// 遍历城市
					if (province.children) {
						for (let city of province.children) {
							if (city.value === numericCode) {
								areaInfo.province = province.label
								areaInfo.city = city.label
								break
							}

							// 遍历区县
							if (city.children) {
								for (let district of city.children) {
									if (district.value === numericCode) {
										areaInfo.province = province.label
										areaInfo.city = city.label
										areaInfo.district = district.label
										break
									}
								}
							}
						}
					}
				}
			}

			// 组合最终的显示文本
			const parts = []
			if (areaInfo.province) parts.push(areaInfo.province)
			if (areaInfo.city) parts.push(areaInfo.city)
			if (areaInfo.district) parts.push(areaInfo.district)

			return parts.join(' ')
		},

		// 查找单个区域代码对应的文本
		findSingleAreaText(areaCode) {
			if (!areaCode || !this.areaData) {
				return null
			}

			// 将区域代码转换为数字进行比较
			const numericAreaCode = parseInt(areaCode)

			// 遍历省份
			for (let province of this.areaData) {
				if (province.value === numericAreaCode) {
					return province.label
				}

				// 遍历城市
				if (province.children) {
					for (let city of province.children) {
						if (city.value === numericAreaCode) {
							return `${province.label} ${city.label}`
						}

						// 遍历区县
						if (city.children) {
							for (let district of city.children) {
								if (district.value === numericAreaCode) {
									return `${province.label} ${city.label} ${district.label}`
								}
							}
						}
					}
				}
			}

			console.warn('NormalSelectEx: 找不到对应的省市区数据', { areaCode, numericAreaCode })
			return null
		}
	}
}
</script>

<style>
</style>