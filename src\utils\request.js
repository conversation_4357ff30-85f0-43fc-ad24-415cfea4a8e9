import store from "@/store";
import { RandomString, Encrypt, Decrypt } from '@/utils/secret';
import LoginApis from '@/apis/modules/user.js'
import { baseUrl } from '@/utils/common.js'

// 加密状态
const securityStatus = uni.getStorageSync("securityStatus") != 'undefined' ? uni.getStorageSync("securityStatus") : false

const request = () => {
    uni.$u.http.setConfig((config) => {
        /* config 为默认全局配置*/
        // #ifdef H5
        config.baseURL = process.env.VUE_APP_BASE_API
        // #endif
        // #ifndef H5
        config.baseURL = process.env.VUE_APP_BASE_API_HOST + process.env.VUE_APP_BASE_API
        // #endif
        config.timeout = 20000
        return config
    })
    // 请求拦截
    uni.$u.http.interceptors.request.use((config) => { // 可使用async await 做异步操作
        config.header = { ...config.header }
        if (store.state.token) {
            config.header['Authorization'] = `${store.state.token}`
        }
				if (store.state.state4Headers) {
					config.header['state'] = store.state.state4Headers
				}
				config.header['client-id'] = 'wx'
        if (securityStatus && config.method.toUpperCase() === 'POST' && config.data) {
            const key = RandomString(16)
            config.header['secret-key'] = key
            const jsonData = JSON.stringify(config.data)
            const data = Encrypt(jsonData, key)
            config.data = { data }
        }
        return config
    }, config => { // 可使用async await 做异步操作
        return Promise.reject(config)
    })
    // 响应拦截
    uni.$u.http.interceptors.response.use((response) => { /* 对响应成功做点什么 可使用async await 做异步操作*/
        if (response.statusCode === 200) {
            if (securityStatus) {
                const data = Decrypt(response.data, response.header['secret-key'])
                let result = data ? JSON.parse(data) : null
                return result?.data || null
            } else {
                return response.data.data
            }
        } else if (response.statusCode === 401) {
            if (securityStatus) {
                const data = Decrypt(response.data, response.header['secret-key'])
                let result = data ? JSON.parse(data) : null
                return Promise.reject(result?.data || null)
            } else {
                return Promise.reject(response.data.data)
            }
        } else {
            return Promise.reject(response.data || { code: 500, message: '未知错误' })
        }
    }, (error) => {
        // 对响应错误做点什么 （statusCode !== 200）
        const statusCode = error?.response?.status || error?.statusCode || 500
        if (statusCode === 'ECONNABORTED') {
            uni.showToast({ title: '网络异常，请稍后重试。', icon: 'none', duration: 3000 })
            return Promise.reject({ code: 500, message: '网络异常，请稍后重试。' })
        }
				// error.response?.data || error.data || 
        let errorData = { code: statusCode, message: error.data.message }
        if (statusCode === 404) {
            errorData.message = '请求的资源不存在'
            uni.showToast({ title: errorData.message, icon: 'none', duration: 3000 })
        } else if (statusCode === 401) {
            errorData.message = '您尚未登录，请登录后继续操作'
            uni.showModal({
                title: '温馨提示',
                content: errorData.message,
                showCancel: false,
                success({ confirm }) {
                    if (confirm) {
                      LoginApis.getWxConfig().then(res => {
                      	store.commit('SET_HOST', res.redirectHost)
                        window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res.appId}&redirect_uri=${res.redirectHost}/${ baseUrl }/pages/login/auth&response_type=code&scope=snsapi_userinfo&state=${res.state}#wechat_redirect`
                      })
                    }
                }
            })
        } else if (statusCode === 500 || statusCode == 6001 || statusCode == 701) {
					uni.showModal({
						title: '提示',
						content: errorData.message,
						showCancel: false
					})
        } else {
            uni.showToast({ title: errorData.message, icon: 'none', duration: 3000 })
        }
        return Promise.reject(errorData)
    })
    return uni.$u.http
}

export default request()