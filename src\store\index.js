import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import createPersistedState from 'vuex-persistedstate'
import userApis from '@/apis/modules/user'

Vue.use(Vuex) // 使用Vuex

const getDefaultState = () => ({
		userId: '',
    token: null,
		state4Headers: '',
    userInfo: null,
    targetUrl: '',
		role: null,
		isFive: null,
		deptCode: 130202, // 区县编码
		isBind: null,
		isDefaultPassword: null,
		isLoginConfirm: null,
		// 域名，展示图片时使用
		appHost: '',
		entry1: '',
		entry2: '',
		entry3: '',
		// 报名学校详情
		schoolDetail: {},
		// 录取查询结果
		applyQryResult: {},
		// 报名详情页是否显示重新报名和修改报名，1不显示修改和重新报名按钮, 2显示
		showModifyTp: 1,
		// 登录方式：wx=微信登录，prov=省平台登录，jsb=冀时办登录
		loginTp: ''
})

export default new Vuex.Store({
    // #ifdef H5
    plugins: [
			createPersistedState({
				key: 'shida-app-vuex',
				storage: uni.storage
			})
		],
		state: getDefaultState(),
		mutations: {
			SET_RESETSTATE(state) {
			  state = Object.assign(state, getDefaultState())
			},
			SET_USER_ID(state, val) {
			  state.userId = val
			},
			SET_TOKEN(state, token) {
			    state.token = token
			},
			SET_STATE4_HEADERS(state, val) {
				state.state4Headers = val
			},
			SET_USERINFO(state, userInfo) {
			    state.userInfo = userInfo
			},
			SET_TARGETURL(state, val) {
			  state.targetUrl = val
			},
			SET_ROLE(state, val) {
			  state.role = val
			},
			SET_ISFIVE(state, val) {
			  state.isFive = val
			},
			SET_DEPTCODE(state, val) {
			  state.deptCode = val
			},
			SET_ISBIND(state, val) {
			  state.isBind = val
			},
			SET_ISDEFAULTPASSWORD(state, val) {
			  state.isDefaultPassword = val
			},
			SET_ISLOGINCONFIRM(state, val) {
			  state.isLoginConfirm = val
			},
			SET_HOST(state, v) {
				state.appHost = v
			},
			SET_ENTRY1(state, v) {
				state.entry1 = v
			},
			SET_ENTRY2(state, v) {
				state.entry2 = v
			},
			SET_ENTRY3(state, v) {
				state.entry3 = v
			},
			SET_SCHOOL_DETAIL(state, v) {
				state.schoolDetail = v
			},
			SET_APPLY_QRY_RES(state, v) {
				state.applyQryResult = v
			},
			SET_SHOW_MODIFY_TP(state, v) {
				state.showModifyTp = v
			},
			SET_ENTITLED_GROUP_Type(state, v) {
				state.entitledGroupType = v
			},
			// 冀时办登录
			setLoginTp(state, v) {
				state.loginTp = v
			}
		},
		actions: {
			login({ commit }, auth) {
				return new Promise((resolve, reject) => {
					userApis.login(auth).then(data => {
							commit('SET_USER_ID', data.id)
							commit('SET_TOKEN', data.token)
							commit('SET_USERINFO', data)
							commit('SET_ROLE', data.roleCode)
							commit('SET_ISFIVE', data.isFive)
							// commit('SET_DEPTCODE', data.deptCode)
							//commit('SET_DEPTCODE', 130433)
							commit('SET_ISBIND', data.binding)
							commit('SET_ISDEFAULTPASSWORD', data.defaultPasswordFlag)
							commit('SET_STATE4_HEADERS', data.state)
							resolve(data)
					}).catch(err => {
							reject(err)
					})
				})
			},
			logout({ commit }) {
			    return new Promise((resolve) => {
			        userApis.logout().finally(() => {
			            resolve()
			            commit('SET_TOKEN', null)
			            commit('SET_USERINFO', null)
			        })
			    })
			},
			getLoginStatus({ commit }) {
					return new Promise((resolve, reject) => {
							userApis.getLoginStatus()
									.then(data => resolve(data))
									.catch(err => reject(err))
					})
			},
			clearState({ commit }) {
					commit('SET_TOKEN', null)
					commit('SET_USERINFO', null)
			}
		}
    // #endif
    // modules,
    // getters
})