<template>
	<view class="school-list">
		<u-search shape="square" placeholder="请输入学校名称" v-model="search.keywords" @change="debounceQry" :showAction="false"></u-search>
		<u-list @scrolltolower="nextPage" class="s-list" v-if="mainList.list.length > 0">
			<u-list-item v-for="(item, idx) in mainList.list" :key="idx" class="s-item">
				<view class="s-i-name">{{ item.deptName }}</view>
				<view class="s-i-actions">
					<view class="action-btn detail-btn" @click="seeDetail(item)">查看学校风采</view>
					<view class="action-btn ad-btn" @click="go2AdForm(item)">报名</view>
				</view>
			</u-list-item>
		</u-list>
		<u-empty mode="list" text="暂无学校" v-else></u-empty>
		<u-modal :show="rejectSchool.show" :title="rejectSchool.title" :content='rejectSchool.content'></u-modal>
	</view>
</template>

<script>
import { schoolList } from "@/apis/admission.js"
import List from '@/mixins/list.js'
export default {
	mixins: [List],
	data() {
		return {
			search: {
			  keywords: "",
				// 乡镇/城区
			  nature: this.$store.state.entry1 - 1,
				// 学段
			  period: "",
				deptCode: this.$store.state.deptCode,
			  type: 1
			},
			// 已被学校驳回
			rejectSchool: {
				show: false,
				title: '提示',
				content: '已被当前学校驳回报名，请选择其它学校'
			}
		}
	},
	created() {
		this.$store.commit('SET_SCHOOL_DETAIL', {})
		let entry2 = this.$store.state.entry2
		// 城区幼儿园
		if (entry2 == 62) {
			this.search.period = 1
		} else if (entry2 == 7 || entry2 == 63) {
			// 7乡镇小学，63城区小学
			this.search.period = 2
		} else if (entry2 == 8 || entry2 == 64) {
			// 8乡镇初中，64城区初中
			this.search.period = 3
		}
		this.getList()
	},
	methods: {
		// 获取列表
		getList() {
			schoolList(this.search).then(res => {
				let newPageData = res.records
				let entry3 = this.$store.state.entry3

				this.mainList.list = this.mainList.list.concat(newPageData)
				// 不足一页
				if (newPageData.length < this.search.pageSize) {
					this.mainList.alLoad = this.mainList.total
					this.mainList.status = 'nomore'
				} else {
					this.mainList.alLoad = this.mainList.list.length
				}
				uni.stopPullDownRefresh()
			})
		},
		// 输入框change防抖
		debounceQry() {
			this.mainList.list = []
			uni.$u.debounce(this.getList, 800)
		},
		// 查看学校风采
		seeDetail(item) {
			this.$store.commit('SET_SCHOOL_DETAIL', item)
			uni.navigateTo({
				url: '/pages/school-list/detail'
			})
		},
		// 前往报名
		go2AdForm(item) {
			this.$store.commit('SET_SCHOOL_DETAIL', item)
			uni.navigateTo({
				url: `/pages/ad-form/index`
			})
		}
	}
}
</script>

<style scoped lang="scss">
.school-list {
	padding: 10px;
	.s-list {
		padding: 15px 0;
	}
	.s-item {
		padding: 10px;
		padding-bottom: 0;
		margin-bottom: 10px;
		border: 1px solid #dcdfe6;
		background-color: #FFF;
		border-radius: 4px;
	}
	.s-i-name {
		text-align: center;
		margin-bottom: 10px;
	}
	.s-i-actions {
		display: flex;
		justify-content: space-between;
		align-items: center;
		text-align: center;
		.action-btn {
			flex: 0 0 30%;
			padding: 10px;
			font-size: 14px;
		}
		.detail-btn {
			
		}
		.ad-btn {
			color: #5ac725;
		}
	}
}
</style>